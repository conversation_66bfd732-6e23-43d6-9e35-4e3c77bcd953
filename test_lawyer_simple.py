#!/usr/bin/env python3
"""
律师推荐系统简单测试脚本（不使用AI）
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_lawyer_data():
    """测试律师数据加载和匹配功能"""
    print("🧪 开始测试律师推荐系统（数据部分）...")
    
    try:
        # 直接导入和测试数据加载
        from agents.lawyer_recommendation_agent import LawyerRecommendationAgent
        
        # 创建实例但不初始化AI部分
        agent = LawyerRecommendationAgent.__new__(LawyerRecommendationAgent)
        agent.lawyer_teams = agent.load_lawyer_teams()
        
        print(f"✅ 已加载 {len(agent.lawyer_teams)} 个律师团队")
        
        # 显示律师团队信息
        for i, team in enumerate(agent.lawyer_teams, 1):
            print(f"{i}. {team['name']} - {team['location']} - {', '.join(team['specialties'])}")
        
        # 测试匹配功能
        test_cases = [
            "我需要处理一个合同纠纷案件",
            "我要离婚，涉及财产分割",
            "我遇到了工伤问题",
            "我的公司需要法律顾问",
            "我需要北京的刑事辩护律师"
        ]
        
        print("\n🔍 测试匹配功能:")
        for i, test_case in enumerate(test_cases, 1):
            print(f"\n测试案例 {i}: {test_case}")
            
            # 测试匹配算法
            matching_teams = agent.find_matching_teams(test_case)
            print(f"   匹配到 {len(matching_teams)} 个律师团队")
            
            if matching_teams:
                for j, team in enumerate(matching_teams[:3], 1):  # 显示前3个
                    score = team.get('match_score', 0)
                    reasons = team.get('match_reasons', [])
                    print(f"   {j}. {team['name']} (分数: {score:.1f}) - {'; '.join(reasons) if reasons else '基础匹配'}")
        
        # 测试格式化功能
        print(f"\n📝 测试格式化功能:")
        test_teams = agent.find_matching_teams("合同纠纷")[:2]
        formatted_result = agent.format_team_recommendation(test_teams)
        print("格式化结果长度:", len(formatted_result), "字符")
        print("包含推荐团队:", len([line for line in formatted_result.split('\n') if '##' in line]))
        
        print("\n✅ 律师推荐系统数据测试完成！")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_lawyer_data()
    sys.exit(0 if success else 1)
