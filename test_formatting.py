#!/usr/bin/env python3
"""
测试律师推荐格式化输出
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_formatting():
    """测试格式化输出"""
    print("🧪 测试律师推荐格式化输出...")
    
    try:
        # 直接导入和测试数据加载
        from agents.lawyer_recommendation_agent import LawyerRecommendationAgent
        
        # 创建实例但不初始化AI部分
        agent = LawyerRecommendationAgent.__new__(LawyerRecommendationAgent)
        agent.lawyer_teams = agent.load_lawyer_teams()
        
        print("✅ 数据加载成功")
        
        # 测试格式化功能
        print("\n📝 测试格式化输出:")
        test_teams = agent.find_matching_teams("合同纠纷")[:2]
        formatted_result = agent.format_team_recommendation(test_teams)
        
        print("="*80)
        print(formatted_result)
        print("="*80)
        
        print(f"\n✅ 格式化测试完成！输出长度: {len(formatted_result)} 字符")
        
        # 测试空结果
        print("\n📝 测试空结果格式化:")
        empty_result = agent.format_team_recommendation([])
        print("="*50)
        print(empty_result)
        print("="*50)
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_formatting()
    sys.exit(0 if success else 1)
