/* 全局样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Aria<PERSON>, sans-serif;
    background: linear-gradient(135deg, #1e3a8a 0%, #1e40af 50%, #b45309 100%);
    min-height: 100vh;
    color: #1f2937;
}

.app-container {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

/* 头部样式 */
.header {
    background: rgba(255, 255, 255, 0.98);
    backdrop-filter: blur(15px);
    border-bottom: 3px solid #b45309;
    padding: 1.2rem 0;
    position: sticky;
    top: 0;
    z-index: 100;
    box-shadow: 0 2px 10px rgba(30, 58, 138, 0.1);
}

.header-content {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.app-title {
    font-size: 2rem;
    font-weight: 800;
    color: #1f2937;
    display: flex;
    align-items: center;
    gap: 0.75rem;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.app-title i {
    color: #b45309;
}

/* 主要内容区域 */
.main-content {
    flex: 1;
    padding: 0;
    width: 100%;
    height: calc(100vh - 80px); /* 减去header高度 */
}

/* 应用布局 */
.app-layout {
    display: flex;
    height: 100%;
    max-width: 1400px;
    margin: 0 auto;
}

/* 侧边栏 */
.sidebar {
    width: 250px;
    background: rgba(255, 255, 255, 0.95);
    border-right: 1px solid rgba(30, 58, 138, 0.2);
    display: flex;
    flex-direction: column;
    height: 100%;
    box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1);
}

.sidebar-header {
    padding: 1.5rem;
    border-bottom: 1px solid rgba(30, 58, 138, 0.2);
}

.sidebar-header h3 {
    font-size: 1.2rem;
    color: #1e3a8a;
    font-weight: 600;
}

.sidebar-nav {
    display: flex;
    flex-direction: column;
    padding: 1rem 0;
    flex: 1;
}

.nav-item {
    display: flex;
    align-items: center;
    padding: 1rem 1.5rem;
    border: none;
    background: transparent;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 1rem;
    font-weight: 500;
    color: #64748b;
    text-align: left;
    border-left: 3px solid transparent;
}

.nav-item i {
    margin-right: 0.75rem;
    font-size: 1.1rem;
    width: 24px;
    text-align: center;
}

.nav-item:hover {
    background: rgba(30, 58, 138, 0.1);
    color: #1e3a8a;
}

.nav-item.active {
    background: rgba(30, 58, 138, 0.15);
    color: #1e3a8a;
    border-left: 3px solid #1e3a8a;
    font-weight: 600;
}

/* 内容区域 */
.content-area {
    flex: 1;
    padding: 1.5rem;
    overflow-y: auto;
    background: rgba(255, 255, 255, 0.8);
    height: 100%;
}

/* 标签页内容 */
.tab-content {
    display: none;
    height: 100%;
    width: 100%;
}

.tab-content.active {
    display: flex;
    flex-direction: column;
}

.tab-header {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 12px;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.tab-header h2 {
    font-size: 1.5rem;
    color: #1e3a8a;
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.tab-header p {
    color: #64748b;
    font-size: 0.95rem;
    margin: 0;
}

/* 按钮样式 */
.btn {
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    font-size: 0.9rem;
    font-weight: 500;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    text-decoration: none;
}

.btn-primary {
    background: linear-gradient(135deg, #1e3a8a 0%, #1e40af 100%);
    color: white;
}

.btn-primary:hover {
    background: linear-gradient(135deg, #1e40af 0%, #2563eb 100%);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(30, 58, 138, 0.4);
}

.btn-secondary {
    background: #e2e8f0;
    color: #4a5568;
}

.btn-secondary:hover {
    background: #cbd5e0;
}

.btn-outline {
    background: transparent;
    color: #1e3a8a;
    border: 2px solid #1e3a8a;
}

.btn-outline:hover {
    background: linear-gradient(135deg, #1e3a8a 0%, #1e40af 100%);
    color: white;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .header-content {
        padding: 0 1rem;
    }

    .app-title {
        font-size: 1.4rem;
    }

    .main-content {
        padding: 0;
        height: calc(100vh - 70px);
    }

    .app-layout {
        flex-direction: column;
    }

    .sidebar {
        width: 100%;
        height: auto;
        border-right: none;
        border-bottom: 1px solid rgba(30, 58, 138, 0.2);
    }

    .sidebar-nav {
        flex-direction: row;
        overflow-x: auto;
        padding: 0.5rem;
    }

    .nav-item {
        white-space: nowrap;
        min-width: 120px;
        justify-content: center;
        padding: 0.75rem 1rem;
        border-left: none;
        border-bottom: 3px solid transparent;
    }

    .nav-item.active {
        border-left: none;
        border-bottom: 3px solid #1e3a8a;
    }

    .content-area {
        padding: 1rem;
        height: calc(100vh - 140px);
    }

    .tab-header h2 {
        font-size: 1.3rem;
    }
}

/* 加载动画 */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: none;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.loading-overlay.show {
    display: flex;
}

.loading-spinner {
    background: white;
    padding: 2rem;
    border-radius: 12px;
    text-align: center;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
}

.loading-spinner i {
    font-size: 2rem;
    color: #667eea;
    margin-bottom: 1rem;
}

.loading-spinner p {
    color: #64748b;
    font-weight: 500;
}

/* 滚动条样式 */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f5f9;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: #cbd5e0;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #a0aec0;
}

/* 案例检索样式 */
.case-search-intro {
    margin-bottom: 2rem;
    padding: 1.5rem;
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    border-radius: 12px;
    border-left: 4px solid #1e3a8a;
}

.search-tips h3 {
    color: #1e3a8a;
    margin-bottom: 1rem;
    font-size: 1.2rem;
}

.search-tips p {
    color: #4a5568;
    margin-bottom: 1rem;
    line-height: 1.6;
}

.search-tips ul {
    margin-bottom: 1.5rem;
    padding-left: 1.5rem;
}

.search-tips li {
    color: #4a5568;
    margin-bottom: 0.5rem;
    line-height: 1.5;
}

.common-crimes h4 {
    color: #2d3748;
    margin-bottom: 1rem;
    font-size: 1rem;
}

.crime-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
}

.crime-tag {
    padding: 0.5rem 1rem;
    background: linear-gradient(135deg, #1e3a8a 0%, #1e40af 100%);
    color: white;
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    user-select: none;
}

.crime-tag:hover {
    background: linear-gradient(135deg, #1e40af 0%, #2563eb 100%);
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(30, 58, 138, 0.3);
}

/* 响应式设计补充 */
@media (max-width: 768px) {
    .crime-tags {
        gap: 0.25rem;
    }
    
    .crime-tag {
        padding: 0.4rem 0.8rem;
        font-size: 0.8rem;
    }
    
    .case-search-intro {
        padding: 1rem;
    }
}
