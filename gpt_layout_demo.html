<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GPT风格布局演示 - LCA 法律咨询助手</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f7f7f8;
            color: #202123;
        }
        .demo-container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 4px 16px rgba(0,0,0,0.1);
        }
        .demo-header {
            text-align: center;
            margin-bottom: 30px;
        }
        .demo-header h1 {
            color: #10a37f;
            margin-bottom: 10px;
        }
        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .feature-card {
            background: #f7f7f8;
            padding: 20px;
            border-radius: 8px;
            border-left: 4px solid #10a37f;
        }
        .feature-card h3 {
            color: #202123;
            margin-bottom: 10px;
        }
        .feature-list {
            list-style: none;
            padding: 0;
        }
        .feature-list li {
            padding: 5px 0;
            color: #374151;
        }
        .feature-list li::before {
            content: "✓ ";
            color: #10a37f;
            font-weight: bold;
        }
        .comparison {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin: 30px 0;
        }
        .comparison-item {
            padding: 20px;
            border-radius: 8px;
        }
        .old-design {
            background: #fee2e2;
            border-left: 4px solid #ef4444;
        }
        .new-design {
            background: #dcfce7;
            border-left: 4px solid #10a37f;
        }
        .cta-section {
            text-align: center;
            padding: 30px;
            background: linear-gradient(135deg, #10a37f, #0e8f6f);
            border-radius: 12px;
            color: white;
            margin-top: 30px;
        }
        .cta-button {
            display: inline-block;
            padding: 12px 24px;
            background: white;
            color: #10a37f;
            text-decoration: none;
            border-radius: 8px;
            font-weight: 600;
            margin-top: 15px;
            transition: all 0.2s ease;
        }
        .cta-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }
        @media (max-width: 768px) {
            .comparison {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="demo-container">
        <div class="demo-header">
            <h1>🎉 全新GPT风格界面上线！</h1>
            <p>LCA 法律咨询助手现已采用现代化的全屏对话界面，提供更优质的用户体验</p>
        </div>

        <div class="feature-grid">
            <div class="feature-card">
                <h3>🎨 现代化设计</h3>
                <ul class="feature-list">
                    <li>类似ChatGPT的全屏布局</li>
                    <li>深色侧边栏导航</li>
                    <li>简洁的对话界面</li>
                    <li>优雅的颜色搭配</li>
                </ul>
            </div>

            <div class="feature-card">
                <h3>💬 优化的对话体验</h3>
                <ul class="feature-list">
                    <li>全屏对话框始终可见</li>
                    <li>消息完美对齐显示</li>
                    <li>流畅的打字动画</li>
                    <li>响应式输入区域</li>
                </ul>
            </div>

            <div class="feature-card">
                <h3>📱 完美响应式</h3>
                <ul class="feature-list">
                    <li>桌面端左右布局</li>
                    <li>移动端上下布局</li>
                    <li>自适应屏幕尺寸</li>
                    <li>触摸友好操作</li>
                </ul>
            </div>

            <div class="feature-card">
                <h3>⚡ 性能优化</h3>
                <ul class="feature-list">
                    <li>更快的页面加载</li>
                    <li>流畅的动画效果</li>
                    <li>优化的内存使用</li>
                    <li>更好的交互反馈</li>
                </ul>
            </div>
        </div>

        <div class="comparison">
            <div class="comparison-item old-design">
                <h3>❌ 旧版设计</h3>
                <ul class="feature-list">
                    <li style="color: #dc2626;">顶部标签页导航</li>
                    <li style="color: #dc2626;">固定高度对话框</li>
                    <li style="color: #dc2626;">复杂的布局结构</li>
                    <li style="color: #dc2626;">传统的UI风格</li>
                </ul>
            </div>

            <div class="comparison-item new-design">
                <h3>✅ 新版设计</h3>
                <ul class="feature-list">
                    <li>左侧导航栏</li>
                    <li>全屏对话界面</li>
                    <li>简洁的布局结构</li>
                    <li>现代化UI风格</li>
                </ul>
            </div>
        </div>

        <div style="background: #f7f7f8; padding: 20px; border-radius: 8px; margin: 20px 0;">
            <h3>🔧 技术改进</h3>
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px;">
                <div>
                    <strong>HTML结构</strong><br>
                    <small>重新设计的语义化结构</small>
                </div>
                <div>
                    <strong>CSS样式</strong><br>
                    <small>现代化的样式系统</small>
                </div>
                <div>
                    <strong>JavaScript</strong><br>
                    <small>优化的交互逻辑</small>
                </div>
                <div>
                    <strong>响应式</strong><br>
                    <small>完美的移动端适配</small>
                </div>
            </div>
        </div>

        <div class="cta-section">
            <h2>🚀 立即体验全新界面</h2>
            <p>现在就访问LCA法律咨询助手，体验类似GPT的全屏对话界面！</p>
            <a href="http://localhost:8000" class="cta-button" target="_blank">
                开始使用新界面
            </a>
        </div>

        <div style="margin-top: 30px; padding: 20px; background: #f0f9ff; border-radius: 8px; border-left: 4px solid #0ea5e9;">
            <h3>📋 功能特性</h3>
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 10px; margin-top: 15px;">
                <div>⚖️ 场景选择</div>
                <div>❓ 法律问答</div>
                <div>🔍 案例检索</div>
                <div>📚 法律学习</div>
                <div>👨‍💼 律师推荐</div>
                <div>⚙️ 个性化设置</div>
            </div>
        </div>
    </div>
</body>
</html>
