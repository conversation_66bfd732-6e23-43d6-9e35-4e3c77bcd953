#!/usr/bin/env python3
"""
API测试脚本
"""

import requests
import json
import time

def test_api():
    """测试API接口"""
    base_url = "http://localhost:5000/api"
    
    print("🧪 开始测试API接口...")
    
    try:
        # 测试健康检查
        print("\n1. 测试健康检查...")
        response = requests.get(f"{base_url}/health", timeout=5)
        print(f"   状态码: {response.status_code}")
        if response.status_code == 200:
            print("   ✅ 健康检查通过")
        else:
            print("   ❌ 健康检查失败")
            return False
        
        # 测试律师推荐介绍
        print("\n2. 测试律师推荐介绍...")
        response = requests.get(f"{base_url}/lawyer_recommendation/intro", timeout=5)
        print(f"   状态码: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                print("   ✅ 律师推荐介绍获取成功")
                print(f"   介绍长度: {len(data.get('data', {}).get('intro', ''))} 字符")
            else:
                print("   ❌ 律师推荐介绍获取失败")
                return False
        else:
            print("   ❌ 律师推荐介绍请求失败")
            return False
        
        # 测试开始律师推荐会话
        print("\n3. 测试开始律师推荐会话...")
        session_data = {
            "session_id": f"test_session_{int(time.time())}"
        }
        response = requests.post(f"{base_url}/lawyer_recommendation/start", 
                               json=session_data, timeout=5)
        print(f"   状态码: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                print("   ✅ 律师推荐会话开始成功")
                session_id = data.get('data', {}).get('session_id')
                print(f"   会话ID: {session_id}")
            else:
                print("   ❌ 律师推荐会话开始失败")
                return False
        else:
            print("   ❌ 律师推荐会话开始请求失败")
            return False
        
        print("\n✅ API测试完成！所有接口正常工作")
        return True
        
    except requests.exceptions.ConnectionError:
        print("❌ 无法连接到API服务器，请确保服务器正在运行")
        return False
    except requests.exceptions.Timeout:
        print("❌ API请求超时")
        return False
    except Exception as e:
        print(f"❌ API测试失败: {str(e)}")
        return False

if __name__ == "__main__":
    success = test_api()
    if success:
        print("\n🎉 所有测试通过！律师推荐系统API正常工作")
    else:
        print("\n💥 测试失败，请检查服务器状态")
