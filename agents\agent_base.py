import json
from abc import ABC, abstractmethod
import hashlib
import time

# from langchain_ollama.chat_models import ChatOllama  # 导入 ChatOllama 模型 (已禁用)
from langchain_core.prompts import ChatPromptTemplate, MessagesPlaceholder  # 导入提示模板相关类
from langchain_core.messages import HumanMessage  # 导入消息类
from langchain_core.runnables.history import RunnableWithMessageHistory  # 导入带有消息历史的可运行类

from .session_history import get_session_history  # 导入会话历史相关方法
from utils.logger import LOG  # 导入日志工具
from langchain_community.chat_models import ChatTongyi  # 导入千问模型
import os

# 简单的响应缓存
response_cache = {}
CACHE_EXPIRY = 300  # 5分钟缓存过期

class AgentBase(ABC):
    """
    抽象基类，提供代理的共有功能。
    设计模式: 抽象基类模式
    核心功能:
            加载系统提示词和初始消息
            初始化千问大模型 (ChatTongyi)
            管理对话历史记录
            提供统一的对话接口

    """
    def __init__(self, name, prompt_file, intro_file=None, session_id=None):
        self.name = name
        self.prompt_file = prompt_file
        self.intro_file = intro_file
        self.session_id = session_id if session_id else self.name
        self.prompt = self.load_prompt()
        self.intro_messages = self.load_intro() if self.intro_file else []
        self.create_chatbot()

    def load_prompt(self):
        """
        从文件加载系统提示语。
        """
        try:
            # 如果是相对路径，转换为绝对路径
            if not os.path.isabs(self.prompt_file):
                # 获取项目根目录
                project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
                prompt_file_path = os.path.join(project_root, self.prompt_file)
            else:
                prompt_file_path = self.prompt_file

            with open(prompt_file_path, "r", encoding="utf-8") as file:
                return file.read().strip()
        except FileNotFoundError:
            raise FileNotFoundError(f"找不到提示文件 {self.prompt_file}!")

    def load_intro(self):
        """
        从 JSON 文件加载初始消息。
        """
        try:
            # 如果是相对路径，转换为绝对路径
            if not os.path.isabs(self.intro_file):
                # 获取项目根目录
                project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
                intro_file_path = os.path.join(project_root, self.intro_file)
            else:
                intro_file_path = self.intro_file

            with open(intro_file_path, "r", encoding="utf-8") as file:
                return json.load(file)
        except FileNotFoundError:
            raise FileNotFoundError(f"找不到初始消息文件 {self.intro_file}!")
        except json.JSONDecodeError:
            raise ValueError(f"初始消息文件 {self.intro_file} 包含无效的 JSON!")

    def create_chatbot(self):
        """
        初始化聊天机器人，包括系统提示和消息历史记录。
        """
        # 创建聊天提示模板，包括系统提示和消息占位符
        system_prompt = ChatPromptTemplate.from_messages([
            ("system", self.prompt),  # 系统提示部分
            MessagesPlaceholder(variable_name="messages"),  # 消息占位符
        ])

        
        # 初始化 ChatOllama 模型，配置参数
        # self.chatbot = system_prompt | ChatOllama(
        #     model="llama3.1:8b-instruct-q8_0",  # 使用的模型名称
        #     max_tokens=8192,  # 最大生成的 token 数
        #     temperature=0.8,  # 随机性配置
        # )

        # 初始化千问模型，配置参数
        from dotenv import load_dotenv, find_dotenv
        _ = load_dotenv(find_dotenv())  # 读取本地 .env 文件，里面定义了 Qwen_API_KEY
        self.chatbot = system_prompt | ChatTongyi(
            model="qwen-turbo",  # 使用千问turbo模型
            dashscope_api_key=os.getenv("Qwen_API_KEY"),  # 从环境变量获取千问API密钥
            max_tokens=2048,  # 减少最大token数以提高响应速度
            temperature=0.7,  # 降低随机性以提高一致性
            streaming=True,  # 启用流式输出以提高响应速度
            top_p=0.8,  # 添加top_p参数优化生成质量
            timeout=30  # 设置超时时间
        )

        # 将聊天机器人与消息历史记录关联
        self.chatbot_with_history = RunnableWithMessageHistory(self.chatbot, get_session_history)

    def chat_with_history(self, user_input, session_id=None):
        """
        处理用户输入，生成包含聊天历史的回复。

        参数:
            user_input (str): 用户输入的消息
            session_id (str, optional): 会话的唯一标识符

        返回:
            str: AI 生成的回复
        """
        if session_id is None:
            session_id = self.session_id

        # 生成缓存键
        cache_key = hashlib.md5(f"{self.name}_{session_id}_{user_input}".encode()).hexdigest()
        current_time = time.time()

        # 检查缓存
        if cache_key in response_cache:
            cached_response, timestamp = response_cache[cache_key]
            if current_time - timestamp < CACHE_EXPIRY:
                LOG.debug(f"[ChatBot][{self.name}] Using cached response")
                return cached_response

        response = self.chatbot_with_history.invoke(
            [HumanMessage(content=user_input)],  # 将用户输入封装为 HumanMessage
            {"configurable": {"session_id": session_id}},  # 传入配置，包括会话ID
        )

        # 缓存响应
        response_cache[cache_key] = (response.content, current_time)

        # 清理过期缓存
        self._cleanup_cache()

        LOG.debug(f"[ChatBot][{self.name}] {response.content}")  # 记录调试日志

        # 格式化输出以确保一致性
        formatted_response = self.format_response(response.content)
        return formatted_response  # 返回格式化后的回复内容

    def _cleanup_cache(self):
        """清理过期的缓存条目"""
        current_time = time.time()
        expired_keys = [
            key for key, (_, timestamp) in response_cache.items()
            if current_time - timestamp >= CACHE_EXPIRY
        ]
        for key in expired_keys:
            del response_cache[key]

    def format_response(self, response_content):
        """
        格式化AI回复内容，确保所有智能体输出格式一致

        参数:
            response_content (str): AI原始回复内容

        返回:
            str: 格式化后的回复内容
        """
        # 如果回复已经包含<br>标签，直接返回
        if '<br>' in response_content:
            return response_content

        # 将换行符转换为<br>标签，确保格式一致
        formatted_content = response_content.replace('\n\n', '<br><br>')
        formatted_content = formatted_content.replace('\n', '<br>')

        return formatted_content
