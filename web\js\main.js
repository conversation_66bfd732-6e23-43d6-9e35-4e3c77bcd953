// 主应用类
class LanguageMentorApp {
    constructor() {
        this.currentTab = 'scenario';
        this.currentScenario = null;
        this.isInitialized = false;
        
        this.init();
    }

    async init() {
        try {
            // 检查API连接
            await this.checkAPIConnection();
            
            // 初始化事件监听器
            this.initEventListeners();
            
            // 加载设置
            this.loadSettings();

            // 初始化头像功能
            this.initAvatarFeature();

                    // 初始化书籍选择功能
        this.initBookSelector();
        
        // 初始化案例检索功能
        this.initCaseSearchFeature();

        // 初始化律师推荐功能
        this.initLawyerRecommendationFeature();

        // 加载词汇学习介绍
        await this.loadVocabIntro();

        // 加载律师推荐介绍
        await this.loadLawyerRecommendationIntro();
            
            this.isInitialized = true;
            console.log('LanguageMentor 应用初始化完成');
            
        } catch (error) {
            console.error('应用初始化失败:', error);
            ErrorHandler.show('应用初始化失败，请检查后端服务是否正常运行');
        }
    }

    async checkAPIConnection() {
        try {
            await api.healthCheck();
            console.log('API 连接正常');
        } catch (error) {
            throw new Error('无法连接到后端服务');
        }
    }

    initEventListeners() {
        // 标签页切换
        document.querySelectorAll('.tab-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const tabId = e.currentTarget.dataset.tab;
                this.switchTab(tabId);
            });
        });

        // 场景选择
        document.querySelectorAll('input[name="scenario"]').forEach(radio => {
            radio.addEventListener('change', (e) => {
                this.handleScenarioChange(e.target.value);
            });
        });

        // 聊天输入框回车发送
        this.initChatInputs();

        // 设置相关
        this.initSettingsModal();

        // 词汇学习下一关按钮
        const nextRoundBtn = document.getElementById('nextRoundBtn');
        if (nextRoundBtn) {
            nextRoundBtn.addEventListener('click', () => {
                this.startNextVocabRound();
            });
        }
    }

    initChatInputs() {
        // 场景聊天
        const scenarioInput = document.getElementById('scenarioInput');
        const scenarioSendBtn = document.getElementById('scenarioSendBtn');
        
        if (scenarioInput && scenarioSendBtn) {
            scenarioInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter' && !scenarioSendBtn.disabled) {
                    this.sendScenarioMessage();
                }
            });
            
            scenarioSendBtn.addEventListener('click', () => {
                this.sendScenarioMessage();
            });
        }

        // 自由对话
        const conversationInput = document.getElementById('conversationInput');
        const conversationSendBtn = document.getElementById('conversationSendBtn');
        
        if (conversationInput && conversationSendBtn) {
            conversationInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    this.sendConversationMessage();
                }
            });
            
            conversationSendBtn.addEventListener('click', () => {
                this.sendConversationMessage();
            });
        }

        // 词汇学习
        const vocabInput = document.getElementById('vocabInput');
        const vocabSendBtn = document.getElementById('vocabSendBtn');
        
        if (vocabInput && vocabSendBtn) {
            vocabInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    this.sendVocabMessage();
                }
            });
            
            vocabSendBtn.addEventListener('click', () => {
                this.sendVocabMessage();
            });
        }

        // 案例检索
        const caseSearchInput = document.getElementById('caseSearchInput');
        const caseSearchSendBtn = document.getElementById('caseSearchSendBtn');
        
        if (caseSearchInput && caseSearchSendBtn) {
            caseSearchInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    this.sendCaseSearchMessage();
                }
            });
            
            caseSearchSendBtn.addEventListener('click', () => {
                this.sendCaseSearchMessage();
            });
        }
    }

    initSettingsModal() {
        const settingsBtn = document.getElementById('settingsBtn');
        const closeSettingsBtn = document.getElementById('closeSettingsBtn');
        const cancelSettingsBtn = document.getElementById('cancelSettingsBtn');
        const saveSettingsBtn = document.getElementById('saveSettingsBtn');
        const settingsModal = document.getElementById('settingsModal');

        // 用于保存打开弹窗时的原始状态
        let originalFormState = {};

        if (settingsBtn) {
            settingsBtn.addEventListener('click', () => {
                // 保存当前表单状态
                originalFormState = this.saveCurrentFormState();

                UIComponents.applySettingsToForm();
                UIComponents.showModal('settingsModal');
            });
        }

        if (closeSettingsBtn) {
            closeSettingsBtn.addEventListener('click', () => {
                // 恢复原始状态
                this.restoreFormState(originalFormState);
                UIComponents.hideModal('settingsModal');
            });
        }

        if (cancelSettingsBtn) {
            cancelSettingsBtn.addEventListener('click', () => {
                // 恢复原始状态
                this.restoreFormState(originalFormState);
                UIComponents.hideModal('settingsModal');
            });
        }

        if (saveSettingsBtn) {
            saveSettingsBtn.addEventListener('click', () => {
                saveSettings();
            });
        }

        // 点击模态框外部关闭
        if (settingsModal) {
            settingsModal.addEventListener('click', (e) => {
                if (e.target === settingsModal) {
                    // 恢复原始状态
                    this.restoreFormState(originalFormState);
                    UIComponents.hideModal('settingsModal');
                }
            });
        }
    }

    // 保存当前表单状态
    saveCurrentFormState() {
        const apiKeyInput = document.getElementById('apiKey');
        const currentUserAvatar = document.getElementById('currentUserAvatar');

        return {
            apiKey: apiKeyInput ? apiKeyInput.value : '',
            userAvatar: currentUserAvatar ? currentUserAvatar.src : 'images/user.png',
            userAvatarData: localStorage.getItem('userAvatar') // 保存localStorage中的头像数据
        };
    }

    // 恢复表单状态
    restoreFormState(state) {
        if (!state) return;

        const apiKeyInput = document.getElementById('apiKey');
        const currentUserAvatar = document.getElementById('currentUserAvatar');

        if (apiKeyInput) {
            apiKeyInput.value = state.apiKey || '';
        }

        if (currentUserAvatar && state.userAvatar) {
            currentUserAvatar.src = state.userAvatar;
        }

        // 恢复localStorage中的头像数据
        if (state.userAvatarData !== undefined) {
            if (state.userAvatarData) {
                localStorage.setItem('userAvatar', state.userAvatarData);
            } else {
                localStorage.removeItem('userAvatar');
            }
        }
    }

    switchTab(tabId) {
        this.currentTab = tabId;
        UIComponents.switchTab(tabId);
        
        // 根据标签页执行特定初始化
        if (tabId === 'scenario') {
            // 场景标签页无需特殊处理
        } else if (tabId === 'conversation') {
            // 自由对话标签页
            UIComponents.setInputState('conversationInput', 'conversationSendBtn', true);
        } else if (tabId === 'vocab') {
            // 词汇学习标签页
            UIComponents.setInputState('vocabInput', 'vocabSendBtn', true);
        } else if (tabId === 'lawyer_recommendation') {
            // 律师推荐标签页
            UIComponents.setInputState('lawyerRecommendationInput', 'lawyerRecommendationSendBtn', true);
        }
    }

    async handleScenarioChange(scenarioId) {
        try {
            this.currentScenario = scenarioId;
            LoadingManager.show('加载场景信息...');

            // 获取场景介绍
            const introResponse = await api.getScenarioIntro(scenarioId);
            if (introResponse.success) {
                UIComponents.updateScenarioIntro(introResponse.data.intro);
            }

            // 开始场景对话
            const sessionId = sessionManager.getSessionId('scenario', scenarioId);
            const startResponse = await api.startScenario(scenarioId, sessionId);
            
            if (startResponse.success) {
                // 清空聊天记录并显示初始消息
                UIComponents.clearMessages('scenarioMessages');
                UIComponents.addMessage('scenarioMessages', startResponse.data.message, false);
                
                // 启用输入框
                UIComponents.setInputState('scenarioInput', 'scenarioSendBtn', true);
            }

        } catch (error) {
            console.error('场景切换失败:', error);
            ErrorHandler.show('场景加载失败: ' + error.message);
        } finally {
            LoadingManager.hide();
        }
    }

    async sendScenarioMessage() {
        const input = document.getElementById('scenarioInput');
        const message = input.value.trim();
        
        if (!message || !this.currentScenario) return;

        try {
            // 添加用户消息
            UIComponents.addMessage('scenarioMessages', message, true);
            input.value = '';
            
            // 禁用输入框
            UIComponents.setInputState('scenarioInput', 'scenarioSendBtn', false);
            
            // 发送消息到API
            const sessionId = sessionManager.getSessionId('scenario', this.currentScenario);
            const response = await api.scenarioChat(this.currentScenario, message, sessionId);
            
            if (response.success) {
                // 添加机器人回复
                await UIComponents.typeMessage('scenarioMessages', response.data.message, false, 30);
            }
            
        } catch (error) {
            console.error('发送场景消息失败:', error);
            ErrorHandler.show('发送消息失败: ' + error.message);
        } finally {
            // 重新启用输入框
            UIComponents.setInputState('scenarioInput', 'scenarioSendBtn', true);
        }
    }

    async sendConversationMessage() {
        const input = document.getElementById('conversationInput');
        const message = input.value.trim();
        
        if (!message) return;

        try {
            // 添加用户消息
            UIComponents.addMessage('conversationMessages', message, true);
            input.value = '';
            
            // 禁用输入框
            UIComponents.setInputState('conversationInput', 'conversationSendBtn', false);
            
            // 发送消息到API
            const sessionId = sessionManager.getSessionId('conversation');
            const response = await api.conversationChat(message, sessionId);
            
            if (response.success) {
                // 添加机器人回复
                await UIComponents.typeMessage('conversationMessages', response.data.message, false, 30);
            }
            
        } catch (error) {
            console.error('发送对话消息失败:', error);
            ErrorHandler.show('发送消息失败: ' + error.message);
        } finally {
            // 重新启用输入框
            UIComponents.setInputState('conversationInput', 'conversationSendBtn', true);
        }
    }

    async sendVocabMessage(customMessage = null) {
        const input = document.getElementById('vocabInput');
        const message = customMessage || input.value.trim();

        if (!message) return;

        try {
            // 只有当不是自定义消息时才添加用户消息到界面
            if (!customMessage) {
                UIComponents.addMessage('vocabMessages', message, true);
                input.value = '';
            }

            // 禁用输入框
            UIComponents.setInputState('vocabInput', 'vocabSendBtn', false);

            // 发送消息到API
            const sessionId = sessionManager.getSessionId('vocab');
            const response = await api.vocabChat(message, sessionId);

            if (response.success) {
                // 添加机器人回复
                await UIComponents.typeMessage('vocabMessages', response.data.message, false, 30);
            }

        } catch (error) {
            console.error('发送词汇消息失败:', error);
            ErrorHandler.show('发送消息失败: ' + error.message);
        } finally {
            // 重新启用输入框
            UIComponents.setInputState('vocabInput', 'vocabSendBtn', true);
        }
    }

    async sendCaseSearchMessage() {
        const caseSearchInput = document.getElementById('caseSearchInput');
        const caseSearchSendBtn = document.getElementById('caseSearchSendBtn');
        const caseSearchMessages = document.getElementById('caseSearchMessages');

        const message = caseSearchInput.value.trim();
        if (!message) return;

        try {
            // 禁用输入框
            UIComponents.setInputState('caseSearchInput', 'caseSearchSendBtn', false);

            // 添加用户消息
            UIComponents.addMessage('caseSearchMessages', message, true);

            // 清空输入框
            caseSearchInput.value = '';

            // 显示加载状态
            LoadingManager.show('正在检索相关案例...');

            // 获取会话ID
            const sessionId = sessionManager.getSessionId('case_search');

            // 调用API
            const response = await api.caseSearchChat(message, sessionId);

            if (response.success) {
                // 显示智能体回复
                await UIComponents.typeMessage('caseSearchMessages', response.data.message, false, 30);
            } else {
                throw new Error(response.error || '案例检索失败');
            }

        } catch (error) {
            console.error('案例检索失败:', error);
            ErrorHandler.show('案例检索失败: ' + error.message);
        } finally {
            // 重新启用输入框
            UIComponents.setInputState('caseSearchInput', 'caseSearchSendBtn', true);
            LoadingManager.hide();
        }
    }

    async startNextVocabRound() {
        try {
            LoadingManager.show('开始新一关...');
            
            // 重置会话
            sessionManager.resetSession('vocab');
            const sessionId = sessionManager.getSessionId('vocab');
            
            // 开始新的词汇学习
            const response = await api.startVocab(sessionId);
            
            if (response.success) {
                // 清空聊天记录并显示新的对话
                UIComponents.clearMessages('vocabMessages');
                UIComponents.addMessage('vocabMessages', response.data.user_message, true);
                await UIComponents.typeMessage('vocabMessages', response.data.bot_message, false, 30);
            }
            
        } catch (error) {
            console.error('开始新一关失败:', error);
            ErrorHandler.show('开始新一关失败: ' + error.message);
        } finally {
            LoadingManager.hide();
        }
    }

    async loadVocabIntro() {
        try {
            const response = await api.getVocabIntro();
            if (response.success) {
                UIComponents.updateVocabIntro(response.data.intro);
            }
        } catch (error) {
            console.error('加载词汇学习介绍失败:', error);
        }
    }

    async loadLawyerRecommendationIntro() {
        try {
            const response = await api.getLawyerRecommendationIntro();
            if (response.success) {
                UIComponents.updateLawyerRecommendationIntro(response.data.intro);
            }
        } catch (error) {
            console.error('加载律师推荐介绍失败:', error);
        }
    }

    initLawyerRecommendationFeature() {
        const lawyerRecommendationInput = document.getElementById('lawyerRecommendationInput');
        const lawyerRecommendationSendBtn = document.getElementById('lawyerRecommendationSendBtn');

        if (lawyerRecommendationInput && lawyerRecommendationSendBtn) {
            // 输入框回车事件
            lawyerRecommendationInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter' && !e.shiftKey) {
                    e.preventDefault();
                    this.sendLawyerRecommendationMessage();
                }
            });

            // 发送按钮点击事件
            lawyerRecommendationSendBtn.addEventListener('click', () => {
                this.sendLawyerRecommendationMessage();
            });
        }
    }

    async sendLawyerRecommendationMessage() {
        const input = document.getElementById('lawyerRecommendationInput');
        const message = input.value.trim();

        if (!message) return;

        try {
            // 禁用输入
            UIComponents.setInputState('lawyerRecommendationInput', 'lawyerRecommendationSendBtn', false);

            // 添加用户消息
            UIComponents.addMessage('lawyerRecommendationMessages', message, true);

            // 清空输入框
            input.value = '';

            // 显示打字指示器
            UIComponents.showTypingIndicator('lawyerRecommendationTyping');

            // 获取会话ID
            const sessionId = sessionManager.getSessionId('lawyer_recommendation');

            // 发送消息到后端
            const response = await api.lawyerRecommendationChat(message, sessionId);

            if (response.success) {
                // 添加机器人回复
                UIComponents.addMessage('lawyerRecommendationMessages', response.data.message, false);
            } else {
                throw new Error(response.error || '律师推荐失败');
            }

        } catch (error) {
            console.error('律师推荐对话失败:', error);
            ErrorHandler.show('律师推荐失败: ' + error.message);
        } finally {
            // 隐藏打字指示器
            UIComponents.hideTypingIndicator('lawyerRecommendationTyping');
            // 重新启用输入
            UIComponents.setInputState('lawyerRecommendationInput', 'lawyerRecommendationSendBtn', true);
        }
    }

    loadSettings() {
        UIComponents.applySettingsToForm();

        // 加载用户头像
        const userAvatar = localStorage.getItem('userAvatar');
        if (userAvatar) {
            const currentUserAvatar = document.getElementById('currentUserAvatar');
            if (currentUserAvatar) {
                currentUserAvatar.src = userAvatar;
            }
        }
    }

    initAvatarFeature() {
        const userAvatarInput = document.getElementById('userAvatarInput');
        const currentUserAvatar = document.getElementById('currentUserAvatar');

        if (userAvatarInput && currentUserAvatar) {
            userAvatarInput.addEventListener('change', (event) => {
                const file = event.target.files[0];
                if (file) {
                    const reader = new FileReader();
                    reader.onload = (e) => {
                        const avatarData = e.target.result;
                        currentUserAvatar.src = avatarData;
                        localStorage.setItem('userAvatar', avatarData);
                    };
                    reader.readAsDataURL(file);
                }
            });
        }
    }

    initBookSelector() {
        const bookOptions = document.querySelectorAll('input[name="book"]');
        const startLearningBtn = document.getElementById('startLearningBtn');
        const bookSelector = document.querySelector('.book-selector');
        const vocabChat = document.getElementById('vocabChat');

        // 监听书籍选择
        bookOptions.forEach(option => {
            option.addEventListener('change', () => {
                if (option.checked) {
                    startLearningBtn.disabled = false;
                }
            });
        });

        // 开始学习按钮点击事件
        if (startLearningBtn) {
            startLearningBtn.addEventListener('click', () => {
                const selectedBook = document.querySelector('input[name="book"]:checked');
                if (selectedBook) {
                    this.startBookLearning(selectedBook.value);
                    bookSelector.style.display = 'none';
                    vocabChat.style.display = 'flex';
                }
            });
        }
    }

    initCaseSearchFeature() {
        // 为罪名标签添加点击事件
        const crimeTags = document.querySelectorAll('.crime-tag');
        crimeTags.forEach(tag => {
            tag.addEventListener('click', () => {
                const caseSearchInput = document.getElementById('caseSearchInput');
                if (caseSearchInput) {
                    caseSearchInput.value = tag.textContent + '案例';
                    caseSearchInput.focus();
                }
            });
        });
    }

    async startBookLearning(bookType) {
        const bookNames = {
            'constitution': '宪法',
            'criminal_law': '刑法',
            'civil_code': '民法典'
        };

        const bookName = bookNames[bookType] || '法律书籍';

        // 清空聊天记录
        const vocabMessages = document.getElementById('vocabMessages');
        vocabMessages.innerHTML = '';

        // 添加重新开始按钮
        this.addRestartButton();

        try {
            // 显示加载状态
            LoadingManager.show('正在为您制定学习计划...');

            // 重置会话并设置书籍记忆
            sessionManager.resetSession('vocab');
            const sessionId = sessionManager.getSessionId('vocab');

            // 调用API开始学习，传入书籍类型
            const response = await api.startVocab(sessionId, bookType);

            if (response.success) {
                // 显示智能体的回复
                await UIComponents.typeMessage('vocabMessages', response.data.bot_message, false, 30);

                // 启用输入框让用户可以回复
                UIComponents.setInputState('vocabInput', 'vocabSendBtn', true);
            } else {
                throw new Error(response.error || '启动学习失败');
            }

        } catch (error) {
            console.error('启动书籍学习失败:', error);
            ErrorHandler.show('启动学习失败: ' + error.message);
        } finally {
            LoadingManager.hide();
        }
    }

    addRestartButton() {
        const vocabChat = document.getElementById('vocabChat');
        const existingBtn = vocabChat.querySelector('.restart-learning-btn');
        if (!existingBtn) {
            const restartBtn = document.createElement('button');
            restartBtn.className = 'btn btn-outline restart-learning-btn';
            restartBtn.innerHTML = '<i class="fas fa-redo"></i> 重新选择书籍';
            restartBtn.style.margin = '1rem';
            restartBtn.onclick = () => this.restartBookSelection();

            vocabChat.insertBefore(restartBtn, vocabChat.firstChild);
        }
    }

    async restartBookSelection() {
        try {
            // 显示加载状态
            LoadingManager.show('正在重置学习状态...');

            // 重置书籍记忆
            const sessionId = sessionManager.getSessionId('vocab');
            await api.resetBookMemory(sessionId);

            // 重置会话
            sessionManager.resetSession('vocab');

            const bookSelector = document.querySelector('.book-selector');
            const vocabChat = document.getElementById('vocabChat');
            const startLearningBtn = document.getElementById('startLearningBtn');

            // 重置选择状态
            const bookOptions = document.querySelectorAll('input[name="book"]');
            bookOptions.forEach(option => option.checked = false);
            startLearningBtn.disabled = true;

            // 显示书籍选择器，隐藏聊天界面
            bookSelector.style.display = 'block';
            vocabChat.style.display = 'none';

            // 清空聊天记录
            UIComponents.clearMessages('vocabMessages');

        } catch (error) {
            console.error('重置书籍选择失败:', error);
            ErrorHandler.show('重置失败: ' + error.message);
        } finally {
            LoadingManager.hide();
        }
    }
}

// 全局函数：切换密码显示
function togglePasswordVisibility(inputId) {
    const input = document.getElementById(inputId);
    const icon = input.parentElement.querySelector('.toggle-password');

    if (input.type === 'password') {
        input.type = 'text';
        icon.className = 'fas fa-eye toggle-password';
        icon.title = '隐藏密码';
    } else {
        input.type = 'password';
        icon.className = 'fas fa-eye-slash toggle-password';
        icon.title = '显示密码';
    }
}

// 全局函数：重置头像
function resetAvatar() {
    const currentUserAvatar = document.getElementById('currentUserAvatar');
    if (currentUserAvatar) {
        currentUserAvatar.src = 'images/user.png';
        localStorage.removeItem('userAvatar');
    }
}

// 全局函数：保存设置
async function saveSettings() {
    const apiKey = document.getElementById('apiKey').value.trim();

    const formData = { apiKey };
    const validation = UIComponents.validateForm(formData);

    if (!validation.isValid) {
        ErrorHandler.show(validation.errors.join('\n'));
        return;
    }

    try {
        LoadingManager.show('保存设置中...');

        // 保存到本地存储
        UIComponents.saveSettings(formData);

        // 更新后端配置
        if (apiKey) {
            await api.updateConfig(apiKey);
        }

        UIComponents.hideModal('settingsModal');
        ErrorHandler.showSuccess('设置保存成功');

    } catch (error) {
        console.error('保存设置失败:', error);
        ErrorHandler.show('保存设置失败: ' + error.message);
    } finally {
        LoadingManager.hide();
    }
}

// 应用启动
document.addEventListener('DOMContentLoaded', () => {
    window.app = new LanguageMentorApp();
});
