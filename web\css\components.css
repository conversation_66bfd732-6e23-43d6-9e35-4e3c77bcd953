/* 场景选择器样式 */
.scenario-selector {
    margin-bottom: 2rem;
}

.scenario-options {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.scenario-option {
    cursor: pointer;
}

.scenario-option input[type="radio"] {
    display: none;
}

.scenario-card {
    padding: 2rem;
    border: 2px solid #e2e8f0;
    border-radius: 12px;
    text-align: center;
    transition: all 0.3s ease;
    background: white;
}

.scenario-card:hover {
    border-color: #1e3a8a;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(30, 58, 138, 0.15);
}

.scenario-option input[type="radio"]:checked + .scenario-card {
    border-color: #1e3a8a;
    background: linear-gradient(135deg, #1e3a8a 0%, #1e40af 100%);
    color: white;
}

.scenario-card i {
    font-size: 3rem;
    margin-bottom: 1rem;
    color: #b45309;
}

.scenario-option input[type="radio"]:checked + .scenario-card i {
    color: white;
}

.scenario-card h3 {
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.scenario-card p {
    color: #64748b;
    font-size: 0.9rem;
}

.scenario-option input[type="radio"]:checked + .scenario-card p {
    color: rgba(255, 255, 255, 0.9);
}

/* 场景介绍样式 */
.scenario-intro {
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    border: 2px solid #e2e8f0;
    border-left: 4px solid #b45309;
    border-radius: 8px;
    padding: 2rem;
    margin-bottom: 2rem;
    color: #374151;
    line-height: 1.7;
    font-weight: 500;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.vocab-intro {
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    border: 2px solid #e2e8f0;
    border-left: 4px solid #b45309;
    border-radius: 8px;
    padding: 2rem;
    margin-bottom: 2rem;
    color: #374151;
    line-height: 1.7;
    font-weight: 500;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

/* 聊天容器样式 */
.chat-container {
    display: flex;
    flex-direction: column;
    height: calc(100vh - 280px);
    border: 1px solid #e2e8f0;
    border-radius: 12px;
    overflow: hidden;
    background: white;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    flex: 1;
}

.chat-messages {
    flex: 1;
    padding: 1.5rem;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.welcome-message {
    text-align: center;
    color: #64748b;
    padding: 2rem;
    background: #f8fafc;
    border-radius: 8px;
    border: 2px dashed #e2e8f0;
}

/* 消息样式 */
.message {
    display: flex;
    margin-bottom: 1.5rem;
    animation: fadeInUp 0.3s ease;
    align-items: flex-start;
    gap: 0.75rem;
}

.message.user {
    flex-direction: row-reverse;
}

.message.bot {
    flex-direction: row;
}

/* 头像样式 */
.message-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    border: 2px solid #e2e8f0;
}

.message-avatar.user-avatar {
    background: white;
    border-color: #e2e8f0;
}

.message-avatar.bot-avatar {
    background: white;
    border-color: #e2e8f0;
}

.message-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 50%;
}

.message-content {
    max-width: 65%;
    padding: 1rem 1.5rem;
    border-radius: 18px;
    word-wrap: break-word;
    line-height: 1.5;
    position: relative;
}

.message.user .message-content {
    background: linear-gradient(135deg, #1e3a8a 0%, #1e40af 100%);
    color: white;
    border-bottom-right-radius: 6px;
}

.message.bot .message-content {
    background: #f8fafc;
    color: #374151;
    border-bottom-left-radius: 6px;
    border: 1px solid #e2e8f0;
}

.message-time {
    font-size: 0.75rem;
    color: #a0aec0;
    margin-top: 0.25rem;
    text-align: right;
}

.message.bot .message-time {
    text-align: left;
}

/* 输入区域样式 */
.chat-input-container {
    border-top: 1px solid #e2e8f0;
    padding: 1rem;
    background: #f8fafc;
}

.chat-input-wrapper {
    display: flex;
    gap: 0.75rem;
    align-items: center;
}

.chat-input {
    flex: 1;
    padding: 0.75rem 1rem;
    border: 1px solid #e2e8f0;
    border-radius: 24px;
    font-size: 1rem;
    outline: none;
    transition: all 0.3s ease;
    background: white;
}

.chat-input:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.chat-input:disabled {
    background: #f1f5f9;
    color: #a0aec0;
    cursor: not-allowed;
}

.send-btn {
    width: 44px;
    height: 44px;
    border: none;
    border-radius: 50%;
    background: linear-gradient(135deg, #1e3a8a 0%, #1e40af 100%);
    color: white;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.send-btn:hover:not(:disabled) {
    background: linear-gradient(135deg, #1e40af 0%, #2563eb 100%);
    transform: scale(1.05);
}

.send-btn:disabled {
    background: #cbd5e0;
    cursor: not-allowed;
    transform: none;
}

/* 模态框样式 */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: none;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.modal.show {
    display: flex;
}

.modal-content {
    background: white;
    border-radius: 12px;
    width: 90%;
    max-width: 500px;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
}

.modal-header {
    padding: 1.5rem;
    border-bottom: 1px solid #e2e8f0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h3 {
    font-size: 1.25rem;
    font-weight: 600;
    color: #2d3748;
}

.modal-close {
    background: none;
    border: none;
    font-size: 1.25rem;
    color: #a0aec0;
    cursor: pointer;
    padding: 0.25rem;
    border-radius: 4px;
    transition: all 0.3s ease;
}

.modal-close:hover {
    color: #1e3a8a;
    background: #f7fafc;
}

.modal-body {
    padding: 1.5rem;
}

.modal-footer {
    padding: 1.5rem;
    border-top: 1px solid #e2e8f0;
    display: flex;
    justify-content: flex-end;
    gap: 1rem;
}

/* 设置表单样式 */
.settings-container {
    max-height: 70vh;
    overflow-y: auto;
    padding: 0.5rem;
}

.setting-section {
    background: #f8fafc;
    border: 1px solid #e2e8f0;
    border-radius: 12px;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
}

.setting-section:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    border-color: #cbd5e1;
}

.setting-title {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 1rem;
    color: #1f2937;
    font-size: 1.1rem;
    font-weight: 600;
    border-bottom: 2px solid #e2e8f0;
    padding-bottom: 0.5rem;
}

.setting-title i {
    color: #1e3a8a;
}

.setting-group {
    margin-bottom: 1rem;
}

.setting-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: #374151;
}

.input-wrapper {
    position: relative;
    display: flex;
    align-items: center;
    background: linear-gradient(145deg, #f8fafc 0%, #ffffff 100%);
    border-radius: 14px;
    padding: 2px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.input-wrapper input {
    flex: 1;
    padding: 1rem 3.5rem 1rem 1.25rem;
    border: 2px solid transparent;
    border-radius: 12px;
    font-size: 1rem;
    background: #ffffff;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.05);
    position: relative;
    z-index: 1;
}

/* 禁用浏览器默认的密码显示/隐藏按钮 */
.input-wrapper input::-ms-reveal,
.input-wrapper input::-ms-clear {
    display: none !important;
    width: 0 !important;
    height: 0 !important;
}

.input-wrapper input::-webkit-credentials-auto-fill-button,
.input-wrapper input::-webkit-caps-lock-indicator {
    display: none !important;
    visibility: hidden !important;
    pointer-events: none !important;
}

/* 针对Edge和Chrome的额外隐藏规则 */
input[type="password"]::-ms-reveal {
    display: none !important;
}

input[type="password"]::-webkit-reveal {
    display: none !important;
}

/* 确保没有额外的padding被添加 */
.input-wrapper input[type="password"] {
    padding-right: 3.5rem !important;
}

/* 额外的浏览器兼容性规则 */
.input-wrapper input[type="password"]::-webkit-textfield-decoration-container {
    display: none !important;
}

.input-wrapper input[type="password"]::-webkit-inner-spin-button,
.input-wrapper input[type="password"]::-webkit-outer-spin-button {
    display: none !important;
}

/* Firefox 特定规则 */
.input-wrapper input[type="password"]::-moz-reveal {
    display: none !important;
}

/* 确保输入框在所有浏览器中都有一致的外观 */
.input-wrapper input[type="password"],
.input-wrapper input[type="text"] {
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
}

.input-wrapper input:focus {
    outline: none;
    border-color: #667eea;
    background: #ffffff;
    box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.05);
}

.input-wrapper:focus-within {
    box-shadow: 0 0 0 4px rgba(102, 126, 234, 0.15),
                0 8px 25px rgba(102, 126, 234, 0.1),
                0 4px 12px rgba(0, 0, 0, 0.08);
    transform: translateY(-2px);
}

.input-wrapper input::placeholder {
    color: #9ca3af;
    font-style: italic;
}

.toggle-password {
    position: absolute;
    right: 1rem;
    top: 50%;
    transform: translateY(-50%);
    cursor: pointer;
    color: #6b7280;
    font-size: 1.1rem;
    transition: color 0.2s ease;
    z-index: 2;
}

.toggle-password:hover {
    color: #374151;
}



.setting-hint {
    display: block;
    margin-top: 0.5rem;
    color: #6b7280;
    font-size: 0.85rem;
    line-height: 1.4;
    padding: 0.5rem 0.75rem;
    background: rgba(59, 130, 246, 0.05);
    border-left: 3px solid #3b82f6;
    border-radius: 0 4px 4px 0;
}

/* API Key 输入框特殊样式 */
.input-wrapper input[type="password"],
.input-wrapper input[type="text"] {
    font-family: 'Courier New', monospace;
    letter-spacing: 0.5px;
}

.input-wrapper input[type="password"]::placeholder,
.input-wrapper input[type="text"]::placeholder {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    letter-spacing: normal;
}

/* 添加输入框的微妙动画效果 */
.input-wrapper {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.input-wrapper:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
    transform: translateY(-1px);
}

/* 确保在所有情况下都只显示我们的自定义眼睛图标 */
.input-wrapper {
    overflow: visible;
}

.input-wrapper input {
    position: relative;
    z-index: 1;
}

/* 强制隐藏所有可能的浏览器默认控件 */
input[type="password"] {
    -webkit-textfield-decoration-container: none !important;
}

input[type="password"]::-webkit-textfield-decoration-container {
    display: none !important;
    visibility: hidden !important;
    opacity: 0 !important;
    width: 0 !important;
    height: 0 !important;
}

/* 头像选择器样式 */
.avatar-selector {
    display: flex;
    align-items: center;
    gap: 1rem;
    flex-wrap: wrap;
}

.current-avatar {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    overflow: hidden;
    border: 3px solid #e2e8f0;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.current-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.avatar-actions {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.avatar-actions .btn {
    padding: 0.5rem 1rem;
    font-size: 0.9rem;
    white-space: nowrap;
}

/* 书籍选择器样式 */
.book-selector {
    background: #f8fafc;
    border: 2px solid #e2e8f0;
    border-radius: 12px;
    padding: 2rem;
    margin-bottom: 2rem;
}

.book-selector h3 {
    text-align: center;
    margin-bottom: 2rem;
    color: #1f2937;
    font-size: 1.5rem;
    font-weight: 600;
}

.book-options {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.book-option {
    cursor: pointer;
}

.book-option input[type="radio"] {
    display: none;
}

.book-card {
    background: white;
    border: 2px solid #e2e8f0;
    border-radius: 12px;
    padding: 2rem;
    text-align: center;
    transition: all 0.3s ease;
    height: 100%;
}

.book-card:hover {
    border-color: #1e3a8a;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(30, 58, 138, 0.15);
}

.book-option input[type="radio"]:checked + .book-card {
    border-color: #1e3a8a;
    background: linear-gradient(135deg, #1e3a8a 0%, #1e40af 100%);
    color: white;
}

.book-card i {
    font-size: 3rem;
    margin-bottom: 1rem;
    color: #b45309;
}

.book-option input[type="radio"]:checked + .book-card i {
    color: #fbbf24;
}

.book-card h4 {
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.book-card p {
    font-size: 0.9rem;
    opacity: 0.8;
    line-height: 1.4;
}

.book-actions {
    text-align: center;
}

.book-actions .btn {
    padding: 0.75rem 2rem;
    font-size: 1.1rem;
}

/* 重新开始按钮样式 */
.restart-learning-btn {
    position: absolute;
    top: 1rem;
    right: 1rem;
    z-index: 10;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(5px);
    border: 2px solid #1e3a8a;
    color: #1e3a8a;
    padding: 0.5rem 1rem;
    border-radius: 8px;
    font-size: 0.9rem;
    transition: all 0.3s ease;
}

.restart-learning-btn:hover {
    background: #1e3a8a;
    color: white;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(30, 58, 138, 0.3);
}
    font-weight: 500;
    color: #4a5568;
}

.setting-group input:not(.input-wrapper input) {
    width: 100%;
    padding: 0.875rem 1rem;
    border: 2px solid #e2e8f0;
    border-radius: 8px;
    font-size: 1rem;
    background: #ffffff;
    transition: all 0.3s ease;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.setting-group input:not(.input-wrapper input):focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1), 0 1px 3px rgba(0, 0, 0, 0.1);
    background: #fefefe;
}

.setting-group input:not(.input-wrapper input)::placeholder {
    color: #9ca3af;
    font-style: italic;
}

/* 动画 */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 响应式设计 */
@media (max-width: 768px) {
    .scenario-options {
        grid-template-columns: 1fr;
    }
    
    .chat-container {
        height: calc(100vh - 240px);
    }
    
    .message-content {
        max-width: 85%;
    }
    
    .modal-content {
        width: 95%;
        margin: 1rem;
    }
    
    .modal-header,
    .modal-body,
    .modal-footer {
        padding: 1rem;
    }
}
