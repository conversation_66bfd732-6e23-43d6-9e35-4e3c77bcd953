<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>消息布局优化演示 - LCA 法律咨询助手</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f7f7f8;
            color: #202123;
        }
        .demo-container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 4px 16px rgba(0,0,0,0.1);
        }
        .demo-header {
            text-align: center;
            margin-bottom: 30px;
        }
        .demo-header h1 {
            color: #10a37f;
            margin-bottom: 10px;
        }
        .comparison-section {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin: 30px 0;
        }
        .layout-demo {
            background: #f7f7f8;
            padding: 20px;
            border-radius: 12px;
            border: 2px solid #e5e7eb;
        }
        .layout-demo h3 {
            text-align: center;
            margin-bottom: 20px;
            color: #202123;
        }
        .old-layout {
            border-color: #ef4444;
        }
        .new-layout {
            border-color: #10a37f;
        }
        .mock-message {
            display: flex;
            padding: 12px 16px;
            margin: 8px 0;
            align-items: flex-start;
            gap: 12px;
        }
        .mock-message.user {
            flex-direction: row-reverse;
        }
        .mock-avatar {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background: #10a37f;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 14px;
            font-weight: bold;
            flex-shrink: 0;
        }
        .mock-avatar.bot {
            background: #6b7280;
        }
        .mock-content {
            max-width: 70%;
            padding: 10px 14px;
            border-radius: 16px;
            font-size: 14px;
            line-height: 1.4;
        }
        .old-layout .mock-content {
            background: #f0f0f0;
            color: #374151;
        }
        .new-layout .mock-message.user .mock-content {
            background: #10a37f;
            color: white;
            border-radius: 16px 16px 4px 16px;
            margin-left: auto;
        }
        .new-layout .mock-message.bot .mock-content {
            background: #f7f7f8;
            color: #374151;
            border-radius: 16px 16px 16px 4px;
            margin-right: auto;
        }
        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }
        .feature-card {
            background: #f7f7f8;
            padding: 20px;
            border-radius: 8px;
            border-left: 4px solid #10a37f;
        }
        .feature-card h3 {
            color: #202123;
            margin-bottom: 10px;
        }
        .feature-list {
            list-style: none;
            padding: 0;
        }
        .feature-list li {
            padding: 5px 0;
            color: #374151;
        }
        .feature-list li::before {
            content: "✓ ";
            color: #10a37f;
            font-weight: bold;
        }
        .cta-button {
            display: inline-block;
            padding: 12px 24px;
            background: #10a37f;
            color: white;
            text-decoration: none;
            border-radius: 8px;
            font-weight: 600;
            margin: 10px 10px 10px 0;
            transition: all 0.2s ease;
        }
        .cta-button:hover {
            background: #0e8f6f;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }
        @media (max-width: 768px) {
            .comparison-section {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="demo-container">
        <div class="demo-header">
            <h1>💬 消息布局优化完成！</h1>
            <p>用户消息现在显示在右侧，AI回复显示在左侧，提供更清晰的对话体验</p>
        </div>

        <div class="comparison-section">
            <div class="layout-demo old-layout">
                <h3>❌ 旧版布局</h3>
                <div class="mock-message user">
                    <div class="mock-avatar">U</div>
                    <div class="mock-content">用户消息和AI回复都在左侧，难以区分</div>
                </div>
                <div class="mock-message bot">
                    <div class="mock-avatar bot">AI</div>
                    <div class="mock-content">AI回复也在左侧，布局单调</div>
                </div>
            </div>

            <div class="layout-demo new-layout">
                <h3>✅ 新版布局</h3>
                <div class="mock-message user">
                    <div class="mock-avatar">U</div>
                    <div class="mock-content">用户消息在右侧，绿色气泡</div>
                </div>
                <div class="mock-message bot">
                    <div class="mock-avatar bot">AI</div>
                    <div class="mock-content">AI回复在左侧，灰色气泡</div>
                </div>
            </div>
        </div>

        <div class="feature-grid">
            <div class="feature-card">
                <h3>🎨 视觉优化</h3>
                <ul class="feature-list">
                    <li>用户消息：右侧绿色气泡</li>
                    <li>AI回复：左侧灰色气泡</li>
                    <li>圆角气泡设计</li>
                    <li>阴影效果增强</li>
                </ul>
            </div>

            <div class="feature-card">
                <h3>📱 布局改进</h3>
                <ul class="feature-list">
                    <li>用户消息右对齐</li>
                    <li>AI消息左对齐</li>
                    <li>头像位置调整</li>
                    <li>时间戳对齐优化</li>
                </ul>
            </div>

            <div class="feature-card">
                <h3>🎯 用户体验</h3>
                <ul class="feature-list">
                    <li>清晰的对话区分</li>
                    <li>类似微信的布局</li>
                    <li>直观的视觉层次</li>
                    <li>更好的可读性</li>
                </ul>
            </div>

            <div class="feature-card">
                <h3>📐 响应式设计</h3>
                <ul class="feature-list">
                    <li>移动端适配优化</li>
                    <li>头像尺寸调整</li>
                    <li>消息宽度自适应</li>
                    <li>间距优化</li>
                </ul>
            </div>
        </div>

        <div style="background: #dcfce7; padding: 20px; border-radius: 8px; border-left: 4px solid #10a37f; margin: 30px 0;">
            <h3>🔧 技术实现细节</h3>
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin-top: 15px;">
                <div>
                    <strong>CSS Flexbox</strong><br>
                    <small>使用 flex-direction: row-reverse 实现右对齐</small>
                </div>
                <div>
                    <strong>气泡样式</strong><br>
                    <small>不同的 border-radius 和背景色</small>
                </div>
                <div>
                    <strong>头像优化</strong><br>
                    <small>调整尺寸和边框样式</small>
                </div>
                <div>
                    <strong>阴影效果</strong><br>
                    <small>box-shadow 增强视觉层次</small>
                </div>
            </div>
        </div>

        <div style="text-align: center; margin-top: 30px;">
            <h3>🚀 立即体验新布局</h3>
            <p>访问法律咨询助手，体验全新的消息布局设计！</p>
            
            <a href="http://localhost:8000" class="cta-button" target="_blank">
                体验新布局
            </a>
            
            <a href="javascript:void(0)" class="cta-button" onclick="testChat()">
                测试对话功能
            </a>
        </div>

        <div style="margin-top: 30px; padding: 20px; background: #f0f9ff; border-radius: 8px; border-left: 4px solid #0ea5e9;">
            <h3>📋 测试建议</h3>
            <ol>
                <li>选择任意功能模块（如法律问答）</li>
                <li>发送几条测试消息</li>
                <li>观察用户消息是否在右侧显示</li>
                <li>验证AI回复是否在左侧显示</li>
                <li>检查气泡样式和颜色是否正确</li>
                <li>测试移动端的显示效果</li>
            </ol>
        </div>
    </div>

    <script>
        function testChat() {
            window.open('http://localhost:8000', '_blank');
            setTimeout(() => {
                alert('消息布局测试指南：\n\n1. 选择任意功能模块\n2. 发送测试消息\n3. 观察消息布局：\n   • 用户消息：右侧绿色气泡\n   • AI回复：左侧灰色气泡\n4. 验证头像位置是否正确\n\n请在新窗口中进行测试！');
            }, 1000);
        }
    </script>
</body>
</html>
