#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
案例检索功能测试脚本
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from agents.case_search_agent import CaseSearchAgent
from utils.logger import LOG

def test_case_search():
    """测试案例检索功能"""
    print("🧪 开始测试案例检索功能...")
    
    # 创建案例检索智能体
    agent = CaseSearchAgent()
    
    # 测试用例
    test_cases = [
        "盗窃案例",
        "诈骗案件",
        "故意伤害案例",
        "未成年人盗窃案例",
        "网络诈骗案例",
        "合同诈骗 缓刑"
    ]
    
    for i, test_input in enumerate(test_cases, 1):
        print(f"\n📋 测试用例 {i}: {test_input}")
        print("-" * 50)
        
        try:
            # 调用案例检索
            response = agent.chat_with_history(test_input)
            print(f"✅ 检索成功")
            print(f"📄 响应内容:")
            print(response[:500] + "..." if len(response) > 500 else response)
            
        except Exception as e:
            print(f"❌ 检索失败: {str(e)}")
        
        print("-" * 50)
    
    print("\n🎉 案例检索功能测试完成！")

def test_case_extraction():
    """测试罪名提取功能"""
    print("\n🔍 测试罪名提取功能...")
    
    agent = CaseSearchAgent()
    
    test_inputs = [
        "我想查找盗窃相关的案例",
        "请帮我检索诈骗罪的判决案例",
        "有没有故意伤害的案例",
        "未成年人犯罪案例",
        "网络诈骗案例检索"
    ]
    
    for test_input in test_inputs:
        crime_type, keywords = agent._extract_search_params(test_input)
        print(f"输入: {test_input}")
        print(f"提取结果: 罪名={crime_type}, 关键词={keywords}")
        print()

if __name__ == "__main__":
    print("🚀 LCA 案例检索功能测试")
    print("=" * 60)
    
    # 测试罪名提取
    test_case_extraction()
    
    # 测试案例检索
    test_case_search()
    
    print("\n✨ 所有测试完成！") 