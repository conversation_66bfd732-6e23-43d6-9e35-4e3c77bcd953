#!/usr/bin/env python3
"""
简单测试格式化功能
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_base_format():
    """测试基类格式化方法"""
    print("🧪 测试基类格式化方法...")
    
    try:
        from agents.agent_base import AgentBase
        
        # 创建一个基类实例来测试格式化方法
        base_agent = AgentBase.__new__(AgentBase)
        
        # 测试不同类型的输入
        test_cases = [
            ("普通文本", "这是一个普通的回复"),
            ("单换行", "这是一个\n包含换行的回复"),
            ("双换行", "这是一个\n\n包含双换行的回复"),
            ("已有br标签", "这是一个<br>已经包含br标签的回复")
        ]
        
        print("\n测试结果:")
        for name, test_input in test_cases:
            formatted = base_agent.format_response(test_input)
            has_br = '<br>' in formatted
            print(f"   {name}: {'✅' if has_br else '❌'}")
            print(f"      输入: {repr(test_input)}")
            print(f"      输出: {repr(formatted)}")
            print()
        
        print("✅ 基类格式化测试完成！")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_base_format()
    sys.exit(0 if success else 1)
