// UI 组件管理类
class UIComponents {
    // 创建消息元素
    static createMessage(content, isUser = false, timestamp = null) {
        const messageDiv = document.createElement('div');
        messageDiv.className = `message ${isUser ? 'user' : 'bot'}`;

        const time = timestamp || new Date().toLocaleTimeString('zh-CN', {
            hour: '2-digit',
            minute: '2-digit'
        });

        // 创建头像
        let avatarSrc;
        if (isUser) {
            avatarSrc = localStorage.getItem('userAvatar') || 'images/user.png';
        } else {
            avatarSrc = 'images/robot.png';
        }
        const avatarTitle = isUser ? '用户' : '法律助手';
        const avatarClass = isUser ? 'user-avatar' : 'bot-avatar';

        messageDiv.innerHTML = `
            <div class="message-avatar ${avatarClass}" title="${avatarTitle}">
                <img src="${avatarSrc}" alt="${avatarTitle}" width="40" height="40">
            </div>
            <div class="message-content">
                ${content}
                <div class="message-time">${time}</div>
            </div>
        `;

        return messageDiv;
    }

    // 添加消息到聊天容器
    static addMessage(containerId, content, isUser = false) {
        const container = document.getElementById(containerId);
        if (!container) return;

        // 移除欢迎消息
        const welcomeMessage = container.querySelector('.welcome-message');
        if (welcomeMessage) {
            welcomeMessage.remove();
        }

        const messageElement = this.createMessage(content, isUser);
        container.appendChild(messageElement);
        
        // 滚动到底部
        container.scrollTop = container.scrollHeight;
        
        return messageElement;
    }

    // 清空聊天消息
    static clearMessages(containerId) {
        const container = document.getElementById(containerId);
        if (!container) return;
        
        container.innerHTML = '';
    }

    // 显示欢迎消息
    static showWelcomeMessage(containerId, message) {
        const container = document.getElementById(containerId);
        if (!container) return;
        
        this.clearMessages(containerId);
        
        const welcomeDiv = document.createElement('div');
        welcomeDiv.className = 'welcome-message';
        welcomeDiv.innerHTML = message;
        
        container.appendChild(welcomeDiv);
    }

    // 设置输入框状态
    static setInputState(inputId, sendBtnId, enabled = true) {
        const input = document.getElementById(inputId);
        const sendBtn = document.getElementById(sendBtnId);
        
        if (input) {
            input.disabled = !enabled;
            if (enabled) {
                input.focus();
            }
        }
        
        if (sendBtn) {
            sendBtn.disabled = !enabled;
        }
    }

    // 切换标签页
    static switchTab(tabId) {
        // 隐藏所有标签页内容
        document.querySelectorAll('.tab-content').forEach(tab => {
            tab.classList.remove('active');
        });
        
        // 移除所有标签按钮的激活状态
        document.querySelectorAll('.tab-btn').forEach(btn => {
            btn.classList.remove('active');
        });
        
        // 显示目标标签页
        const targetTab = document.getElementById(`${tabId}-tab`);
        if (targetTab) {
            targetTab.classList.add('active');
        }
        
        // 激活对应的标签按钮
        const targetBtn = document.querySelector(`[data-tab="${tabId}"]`);
        if (targetBtn) {
            targetBtn.classList.add('active');
        }
    }

    // 显示/隐藏模态框
    static showModal(modalId) {
        const modal = document.getElementById(modalId);
        if (modal) {
            modal.classList.add('show');
        }
    }

    static hideModal(modalId) {
        const modal = document.getElementById(modalId);
        if (modal) {
            modal.classList.remove('show');
        }
    }

    // 更新场景介绍
    static updateScenarioIntro(content) {
        const introElement = document.getElementById('scenarioIntro');
        if (introElement) {
            // 将 Markdown 转换为 HTML（简单处理）
            const htmlContent = this.markdownToHtml(content);
            introElement.innerHTML = htmlContent;
        }
    }

    // 更新词汇学习介绍
    static updateVocabIntro(content) {
        const introElement = document.getElementById('vocabIntro');
        if (introElement) {
            const htmlContent = this.markdownToHtml(content);
            introElement.innerHTML = htmlContent;
        }
    }

    // 简单的 Markdown 转 HTML
    static markdownToHtml(markdown) {
        if (!markdown) return '';
        
        return markdown
            // 标题
            .replace(/^### (.*$)/gim, '<h3>$1</h3>')
            .replace(/^## (.*$)/gim, '<h2>$1</h2>')
            .replace(/^# (.*$)/gim, '<h1>$1</h1>')
            // 粗体
            .replace(/\*\*(.*)\*\*/gim, '<strong>$1</strong>')
            // 斜体
            .replace(/\*(.*)\*/gim, '<em>$1</em>')
            // 代码块
            .replace(/```([\s\S]*?)```/gim, '<pre><code>$1</code></pre>')
            // 行内代码
            .replace(/`([^`]*)`/gim, '<code>$1</code>')
            // 链接
            .replace(/\[([^\]]*)\]\(([^\)]*)\)/gim, '<a href="$2" target="_blank">$1</a>')
            // 换行
            .replace(/\n/gim, '<br>');
    }

    // 获取选中的场景
    static getSelectedScenario() {
        const selectedRadio = document.querySelector('input[name="scenario"]:checked');
        return selectedRadio ? selectedRadio.value : null;
    }

    // 设置场景选择
    static setSelectedScenario(scenarioId) {
        const radio = document.querySelector(`input[name="scenario"][value="${scenarioId}"]`);
        if (radio) {
            radio.checked = true;
        }
    }

    // 禁用/启用场景选择
    static setScenarioSelectionState(enabled = true) {
        const radios = document.querySelectorAll('input[name="scenario"]');
        radios.forEach(radio => {
            radio.disabled = !enabled;
        });
    }

    // 显示打字效果
    static async typeMessage(containerId, content, isUser = false, speed = 50) {
        const container = document.getElementById(containerId);
        if (!container) return;

        // 移除欢迎消息
        const welcomeMessage = container.querySelector('.welcome-message');
        if (welcomeMessage) {
            welcomeMessage.remove();
        }

        const messageElement = this.createMessage('', isUser);
        const contentElement = messageElement.querySelector('.message-content');
        container.appendChild(messageElement);

        // 打字效果
        let i = 0;
        const typeInterval = setInterval(() => {
            if (i < content.length) {
                const currentText = content.substring(0, i + 1);
                const time = new Date().toLocaleTimeString('zh-CN', {
                    hour: '2-digit',
                    minute: '2-digit'
                });
                contentElement.innerHTML = `
                    ${currentText}
                    <div class="message-time">${time}</div>
                `;
                i++;
                container.scrollTop = container.scrollHeight;
            } else {
                clearInterval(typeInterval);
            }
        }, speed);

        return messageElement;
    }

    // 验证表单
    static validateForm(formData) {
        const errors = [];
        
        if (formData.apiKey && formData.apiKey.length < 15) {
            errors.push('千问 API Key 长度不能少于15个字符');
        }
        
        return {
            isValid: errors.length === 0,
            errors
        };
    }

    // 保存设置到本地存储
    static saveSettings(settings) {
        try {
            localStorage.setItem('languageMentorSettings', JSON.stringify(settings));
            return true;
        } catch (error) {
            console.error('保存设置失败:', error);
            return false;
        }
    }

    // 从本地存储加载设置
    static loadSettings() {
        try {
            const settings = localStorage.getItem('languageMentorSettings');
            return settings ? JSON.parse(settings) : {};
        } catch (error) {
            console.error('加载设置失败:', error);
            return {};
        }
    }

    // 应用设置到表单
    static applySettingsToForm() {
        const settings = this.loadSettings();
        
        const apiKeyInput = document.getElementById('apiKey');
        
        if (apiKeyInput && settings.apiKey) {
            apiKeyInput.value = settings.apiKey;
        }
    }

    // 格式化时间戳
    static formatTimestamp(timestamp) {
        const date = new Date(timestamp);
        return date.toLocaleTimeString('zh-CN', {
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit'
        });
    }

    // 检测移动设备
    static isMobile() {
        return window.innerWidth <= 768;
    }

    // 自适应布局调整
    static adjustLayoutForMobile() {
        if (this.isMobile()) {
            document.body.classList.add('mobile');
        } else {
            document.body.classList.remove('mobile');
        }
    }
}

// 键盘快捷键管理
class KeyboardManager {
    static init() {
        document.addEventListener('keydown', (e) => {
            // Ctrl/Cmd + Enter 发送消息
            if ((e.ctrlKey || e.metaKey) && e.key === 'Enter') {
                const activeTab = document.querySelector('.tab-content.active');
                if (activeTab) {
                    const sendBtn = activeTab.querySelector('.send-btn');
                    if (sendBtn && !sendBtn.disabled) {
                        sendBtn.click();
                    }
                }
            }
            
            // Escape 关闭模态框
            if (e.key === 'Escape') {
                const openModal = document.querySelector('.modal.show');
                if (openModal) {
                    openModal.classList.remove('show');
                }
            }
        });
    }
}

// 初始化键盘管理
document.addEventListener('DOMContentLoaded', () => {
    KeyboardManager.init();
    UIComponents.adjustLayoutForMobile();
    
    // 监听窗口大小变化
    window.addEventListener('resize', () => {
        UIComponents.adjustLayoutForMobile();
    });
});
