<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>LCA 法律咨询助手</title>
    <link rel="stylesheet" href="css/main.css">
    <link rel="stylesheet" href="css/components.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="app-container">
        <!-- 头部导航 -->
        <header class="header">
            <div class="header-content">
                <h1 class="app-title">
                    <i class="fas fa-balance-scale"></i>
                    LCA 法律咨询助手
                </h1>
                <div class="header-actions">
                    <button class="btn btn-outline" id="settingsBtn">
                        <i class="fas fa-cog"></i>
                        设置
                    </button>
                </div>
            </div>
        </header>

        <!-- 主要内容区域 -->
        <main class="main-content">
            <!-- 标签页导航 -->
            <nav class="tab-nav">
                <button class="tab-btn active" data-tab="scenario">
                    <i class="fas fa-balance-scale"></i>
                    场景选择
                </button>
                <button class="tab-btn" data-tab="conversation">
                    <i class="fas fa-question-circle"></i>
                    法律问答
                </button>
                <button class="tab-btn" data-tab="case_search">
                    <i class="fas fa-search"></i>
                    案例检索
                </button>
                <button class="tab-btn" data-tab="vocab">
                    <i class="fas fa-gavel"></i>
                    法律学习
                </button>
                <button class="tab-btn" data-tab="lawyer_recommendation">
                    <i class="fas fa-user-tie"></i>
                    律师推荐
                </button>
            </nav>

            <!-- 场景练习标签页 -->
            <div class="tab-content active" id="scenario-tab">
                <div class="tab-header">
                    <h2>选择一个法律场景获取专业咨询</h2>
                </div>

                <div class="scenario-selector">
                    <div class="scenario-options">
                        <label class="scenario-option">
                            <input type="radio" name="scenario" value="marriage_dispute">
                            <div class="scenario-card">
                                <i class="fas fa-heart-broken"></i>
                                <h3>婚姻纠纷</h3>
                                <p>离婚、财产分割、子女抚养等婚姻法律问题</p>
                            </div>
                        </label>
                        <label class="scenario-option">
                            <input type="radio" name="scenario" value="contract_dispute">
                            <div class="scenario-card">
                                <i class="fas fa-file-contract"></i>
                                <h3>合同纠纷</h3>
                                <p>合同效力、违约责任、争议解决等问题</p>
                            </div>
                        </label>
                        <label class="scenario-option">
                            <input type="radio" name="scenario" value="work_injury">
                            <div class="scenario-card">
                                <i class="fas fa-hard-hat"></i>
                                <h3>工伤赔偿</h3>
                                <p>工伤认定、伤残鉴定、赔偿计算等问题</p>
                            </div>
                        </label>
                    </div>
                </div>

                <div class="scenario-intro" id="scenarioIntro">
                    <p>请选择一个法律场景开始咨询</p>
                </div>

                <div class="chat-container" id="scenarioChat">
                    <div class="chat-messages" id="scenarioMessages">
                        <div class="welcome-message">
                            <strong>您的法律咨询助手</strong><br><br>
                            选择法律场景后开始咨询吧！
                        </div>
                    </div>
                    <div class="chat-input-container">
                        <div class="chat-input-wrapper">
                            <input type="text" class="chat-input" id="scenarioInput" placeholder="输入你的回复..." disabled>
                            <button class="send-btn" id="scenarioSendBtn" disabled>
                                <i class="fas fa-paper-plane"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 法律问答标签页 -->
            <div class="tab-content" id="conversation-tab">
                <div class="tab-header">
                    <h2>法律知识问答咨询</h2>
                </div>

                <div class="chat-container" id="conversationChat">
                    <div class="chat-messages" id="conversationMessages">
                        <div class="welcome-message">
                            <strong>您的法律咨询助手</strong><br><br>
                            请提出您的法律问题，我会为您提供专业解答！
                        </div>
                    </div>
                    <div class="chat-input-container">
                        <div class="chat-input-wrapper">
                            <input type="text" class="chat-input" id="conversationInput" placeholder="输入你的消息...">
                            <button class="send-btn" id="conversationSendBtn">
                                <i class="fas fa-paper-plane"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 案例检索标签页 -->
            <div class="tab-content" id="case_search-tab">
                <div class="tab-header">
                    <h2>法律案例检索</h2>
                </div>

                <div class="case-search-intro">
                    <div class="search-tips">
                        <h3>💡 检索提示</h3>
                        <p>请输入罪名类型或关键词，我将为您检索相关法律案例：</p>
                        <ul>
                            <li><strong>直接罪名检索</strong>：如"盗窃案例"、"诈骗案件"</li>
                            <li><strong>关键词检索</strong>：如"未成年人犯罪案例"、"网络诈骗案例"</li>
                            <li><strong>复合检索</strong>：如"盗窃罪 未成年人 缓刑"</li>
                        </ul>
                        <div class="common-crimes">
                            <h4>常见罪名类型：</h4>
                            <div class="crime-tags">
                                <span class="crime-tag">盗窃</span>
                                <span class="crime-tag">诈骗</span>
                                <span class="crime-tag">故意伤害</span>
                                <span class="crime-tag">抢劫</span>
                                <span class="crime-tag">合同诈骗</span>
                                <span class="crime-tag">职务侵占</span>
                                <span class="crime-tag">贪污</span>
                                <span class="crime-tag">受贿</span>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="chat-container" id="caseSearchChat">
                    <div class="chat-messages" id="caseSearchMessages">
                        <div class="welcome-message">
                            <strong>您的案例检索助手</strong><br><br>
                            请输入罪名类型或关键词，我将为您检索相关法律案例！
                        </div>
                    </div>
                    <div class="chat-input-container">
                        <div class="chat-input-wrapper">
                            <input type="text" class="chat-input" id="caseSearchInput" placeholder="输入罪名或关键词...">
                            <button class="send-btn" id="caseSearchSendBtn">
                                <i class="fas fa-paper-plane"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 法律学习标签页 -->
            <div class="tab-content" id="vocab-tab">
                <div class="tab-header">
                    <h2>法律书籍系统学习</h2>
                </div>

                <div class="book-selector">
                    <h3>选择学习书籍</h3>
                    <div class="book-options">
                        <label class="book-option">
                            <input type="radio" name="book" value="constitution">
                            <div class="book-card">
                                <i class="fas fa-landmark"></i>
                                <h4>宪法</h4>
                                <p>国家根本大法，规定国家基本制度和公民基本权利</p>
                            </div>
                        </label>
                        <label class="book-option">
                            <input type="radio" name="book" value="criminal_law">
                            <div class="book-card">
                                <i class="fas fa-gavel"></i>
                                <h4>刑法</h4>
                                <p>规定犯罪和刑罚的法律，维护社会秩序</p>
                            </div>
                        </label>
                        <label class="book-option">
                            <input type="radio" name="book" value="civil_code">
                            <div class="book-card">
                                <i class="fas fa-balance-scale"></i>
                                <h4>民法典</h4>
                                <p>调整平等主体间人身关系和财产关系的法律</p>
                            </div>
                        </label>
                    </div>
                    <div class="book-actions">
                        <button class="btn btn-primary" id="startLearningBtn" disabled>
                            <i class="fas fa-play"></i>
                            开始学习
                        </button>
                    </div>
                </div>

                <div class="chat-container" id="vocabChat" style="display: none;">
                    <div class="chat-messages" id="vocabMessages">
                        <div class="welcome-message">
                            <strong>您的法律教育助手</strong><br><br>
                            开始您的法律学习之旅！
                        </div>
                    </div>
                    <div class="chat-input-container">
                        <div class="chat-input-wrapper">
                            <input type="text" class="chat-input" id="vocabInput" placeholder="输入你的回答...">
                            <button class="send-btn" id="vocabSendBtn">
                                <i class="fas fa-paper-plane"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 律师推荐标签页 -->
            <div class="tab-content" id="lawyer_recommendation-tab">
                <div class="tab-header">
                    <h2>专业律师团队推荐</h2>
                    <p>根据您的法律需求，为您推荐最合适的律师团队</p>
                </div>

                <div class="chat-container">
                    <div class="chat-messages" id="lawyerRecommendationMessages">
                        <div class="message bot-message">
                            <div class="message-avatar">
                                <img src="images/bot.png" alt="助手头像">
                            </div>
                            <div class="message-content">
                                <div class="message-text" id="lawyerRecommendationIntro">
                                    正在加载律师推荐服务...
                                </div>
                                <div class="message-time">
                                    <i class="fas fa-clock"></i>
                                    <span id="lawyerRecommendationIntroTime"></span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="typing-indicator" id="lawyerRecommendationTyping" style="display: none;">
                        <div class="message bot-message">
                            <div class="message-avatar">
                                <img src="images/bot.png" alt="助手头像">
                            </div>
                            <div class="message-content">
                                <div class="typing-dots">
                                    <span></span>
                                    <span></span>
                                    <span></span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="welcome-message" id="lawyerRecommendationWelcome">
                        <div class="welcome-icon">
                            <i class="fas fa-user-tie"></i>
                        </div>
                        <div class="welcome-text">
                            <strong>专业律师推荐服务</strong><br><br>
                            请描述您的法律需求，我会为您推荐最合适的律师团队！
                        </div>
                    </div>
                    <div class="chat-input-container">
                        <div class="chat-input-wrapper">
                            <input type="text" class="chat-input" id="lawyerRecommendationInput" placeholder="请描述您的法律需求...">
                            <button class="send-btn" id="lawyerRecommendationSendBtn">
                                <i class="fas fa-paper-plane"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </main>

        <!-- 设置模态框 -->
        <div class="modal" id="settingsModal">
            <div class="modal-content">
                <div class="modal-header">
                    <h3>设置</h3>
                    <button class="modal-close" id="closeSettingsBtn">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="modal-body">
                    <div class="settings-container">
                        <div class="setting-section">
                            <h4 class="setting-title">
                                <i class="fas fa-key"></i>
                                API 配置
                            </h4>
                            <div class="setting-group">
                                <label for="apiKey">API Key</label>
                                <div class="input-wrapper">
                                    <input type="password" id="apiKey" placeholder="请输入您的 API Key..." autocomplete="new-password" data-lpignore="true">
                                    <i class="fas fa-eye-slash toggle-password" onclick="togglePasswordVisibility('apiKey')" title="显示密码"></i>
                                </div>
                                <small class="setting-hint">用于调用AI服务的密钥，请妥善保管</small>
                            </div>
                        </div>

                        <div class="setting-section">
                            <h4 class="setting-title">
                                <i class="fas fa-user-circle"></i>
                                个人设置
                            </h4>
                            <div class="setting-group">
                                <label>用户头像</label>
                                <div class="avatar-selector">
                                    <div class="current-avatar">
                                        <img id="currentUserAvatar" src="images/user.png" alt="当前头像" width="40" height="40">
                                    </div>
                                    <div class="avatar-actions">
                                        <input type="file" id="userAvatarInput" accept="image/*" style="display: none;">
                                        <button type="button" class="btn btn-outline" onclick="document.getElementById('userAvatarInput').click()">
                                            <i class="fas fa-upload"></i>
                                            选择头像
                                        </button>
                                        <button type="button" class="btn btn-outline" onclick="resetAvatar()">
                                            <i class="fas fa-undo"></i>
                                            重置默认
                                        </button>
                                    </div>
                                </div>
                                <small class="setting-hint">支持 JPG、PNG 格式，建议尺寸 200x200 像素</small>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button class="btn btn-secondary" id="cancelSettingsBtn">取消</button>
                    <button class="btn btn-primary" id="saveSettingsBtn">保存</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 加载提示 -->
    <div class="loading-overlay" id="loadingOverlay">
        <div class="loading-spinner">
            <i class="fas fa-spinner fa-spin"></i>
            <p>正在处理...</p>
        </div>
    </div>

    <script src="js/api.js"></script>
    <script src="js/components.js"></script>
    <script src="js/main.js"></script>
</body>
</html>
