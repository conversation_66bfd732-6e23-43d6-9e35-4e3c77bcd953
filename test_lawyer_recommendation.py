#!/usr/bin/env python3
"""
律师推荐系统测试脚本
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from agents.lawyer_recommendation_agent import LawyerRecommendationAgent

def test_lawyer_recommendation():
    """测试律师推荐功能"""
    print("🧪 开始测试律师推荐系统...")
    
    try:
        # 创建律师推荐智能体
        agent = LawyerRecommendationAgent()
        print("✅ 律师推荐智能体创建成功")
        
        # 测试数据加载
        print(f"📊 已加载 {len(agent.lawyer_teams)} 个律师团队")
        
        # 测试匹配功能
        test_cases = [
            "我需要处理一个合同纠纷案件",
            "我要离婚，涉及财产分割",
            "我遇到了工伤问题",
            "我的公司需要法律顾问",
            "我需要北京的刑事辩护律师"
        ]
        
        for i, test_case in enumerate(test_cases, 1):
            print(f"\n🔍 测试案例 {i}: {test_case}")
            
            # 测试匹配算法
            matching_teams = agent.find_matching_teams(test_case)
            print(f"   匹配到 {len(matching_teams)} 个律师团队")
            
            if matching_teams:
                top_team = matching_teams[0]
                print(f"   推荐: {top_team['name']} (匹配分数: {top_team.get('match_score', 0):.1f})")
        
        print("\n✅ 律师推荐系统测试完成！")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_lawyer_recommendation()
    sys.exit(0 if success else 1)
