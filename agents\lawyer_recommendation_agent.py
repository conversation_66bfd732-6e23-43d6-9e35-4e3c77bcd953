import json
import os
from typing import List, Dict, Any
from .agent_base import AgentBase
from utils.logger import LOG

class LawyerRecommendationAgent(AgentBase):
    """
    律师团队推荐智能体
    功能:
    - 根据用户需求推荐合适的律师团队
    - 提供律师团队详细信息
    - 智能匹配专业领域
    """
    
    def __init__(self, session_id=None):
        super().__init__(
            name="lawyer_recommendation",
            prompt_file="prompts/lawyer_recommendation_prompt.txt",
            intro_file="content/intro/lawyer_recommendation_intro.json",
            session_id=session_id
        )
        self.lawyer_teams = self.load_lawyer_teams()
        LOG.info(f"[LawyerRecommendationAgent] 初始化完成，加载了 {len(self.lawyer_teams)} 个律师团队")
    
    def load_lawyer_teams(self) -> List[Dict[str, Any]]:
        """从JSON文件加载律师团队数据"""
        try:
            # 获取项目根目录
            project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
            json_file_path = os.path.join(project_root, 'data', 'lawyer_teams.json')

            with open(json_file_path, 'r', encoding='utf-8') as f:
                lawyer_teams = json.load(f)

            LOG.info(f"[LawyerRecommendationAgent] 从JSON文件加载了 {len(lawyer_teams)} 个律师团队")
            return lawyer_teams

        except FileNotFoundError:
            LOG.error(f"[LawyerRecommendationAgent] 律师团队数据文件未找到: {json_file_path}")
            return self._get_fallback_data()
        except json.JSONDecodeError as e:
            LOG.error(f"[LawyerRecommendationAgent] JSON文件解析错误: {str(e)}")
            return self._get_fallback_data()
        except Exception as e:
            LOG.error(f"[LawyerRecommendationAgent] 加载律师团队数据失败: {str(e)}")
            return self._get_fallback_data()

    def _get_fallback_data(self) -> List[Dict[str, Any]]:
        """获取备用数据（当JSON文件加载失败时使用）"""
        LOG.warning("[LawyerRecommendationAgent] 使用备用律师团队数据")
        return [
            {
                "id": 1,
                "name": "北京德恒律师事务所",
                "location": "北京",
                "specialties": ["公司法", "金融法", "知识产权", "国际贸易"],
                "rating": 4.8,
                "experience_years": 25,
                "team_size": 150,
                "description": "德恒律师事务所是中国领先的综合性律师事务所之一，在公司法务、金融证券、知识产权等领域具有丰富经验。",
                "contact": {
                    "phone": "010-52682888",
                    "email": "<EMAIL>",
                    "address": "北京市朝阳区东三环中路1号环球金融中心",
                    "website": "www.dehenglaw.com"
                },
                "notable_cases": [
                    "某大型国企重组法律服务",
                    "跨国并购交易法律顾问",
                    "知识产权侵权诉讼胜诉"
                ],
                "lawyers": [
                    {"name": "王德恒", "title": "高级合伙人", "specialty": "公司法"},
                    {"name": "李明华", "title": "合伙人", "specialty": "金融法"},
                    {"name": "张晓雯", "title": "合伙人", "specialty": "知识产权"}
                ]
            }
        ]
    
    def find_matching_teams(self, user_requirements: str, specialty: str = None, location: str = None) -> List[Dict[str, Any]]:
        """
        根据用户需求匹配律师团队
        
        参数:
            user_requirements: 用户需求描述
            specialty: 专业领域
            location: 地理位置
            
        返回:
            匹配的律师团队列表
        """
        matching_teams = []
        
        # 关键词映射
        specialty_keywords = {
            "婚姻": ["婚姻家庭"],
            "离婚": ["婚姻家庭"],
            "合同": ["合同纠纷", "公司法"],
            "工伤": ["劳动法"],
            "劳动": ["劳动法"],
            "刑事": ["刑事辩护"],
            "民事": ["民事诉讼"],
            "知识产权": ["知识产权"],
            "公司": ["公司法", "公司治理"],
            "投资": ["创业投资", "证券法"],
            "房地产": ["房地产"],
            "建设": ["建设工程"],
            "工程": ["建设工程"],
            "互联网": ["互联网法", "电子商务"],
            "金融": ["金融法", "证券法", "互联网金融"],
            "电商": ["电子商务", "互联网法"],
            "数据": ["数据保护"],
            "环境": ["环境法"],
            "行政": ["行政诉讼", "政府法律顾问"],
            "医疗": ["医疗纠纷"],
            "保险": ["保险理赔"],
            "消费": ["消费者权益"],
            "产品": ["产品责任"],
            "能源": ["能源法"],
            "矿业": ["矿业法"],
            "基建": ["基础设施"],
            "海商": ["海商法"],
            "贸易": ["国际贸易"],
            "物流": ["物流运输"],
            "港口": ["港口法务"],
            "PPP": ["PPP项目"],
            "政府": ["政府法律顾问"]
        }
        
        # 提取关键词
        relevant_specialties = set()
        user_req_lower = user_requirements.lower()
        
        for keyword, specialties in specialty_keywords.items():
            if keyword in user_req_lower:
                relevant_specialties.update(specialties)
        
        if specialty:
            relevant_specialties.add(specialty)
        
        # 匹配律师团队
        for team in self.lawyer_teams:
            score = 0
            
            # 专业领域匹配
            team_specialties = set(team["specialties"])
            specialty_match = len(relevant_specialties.intersection(team_specialties))
            score += specialty_match * 10
            
            # 地理位置匹配
            if location and location in team["location"]:
                score += 5
            
            # 评分和经验加权
            score += team["rating"]
            score += min(team["experience_years"] / 5, 10)  # 经验年限加分，最多10分
            
            if score > 0:
                team_copy = team.copy()
                team_copy["match_score"] = score
                team_copy["match_reasons"] = []
                
                if specialty_match > 0:
                    matched_specs = relevant_specialties.intersection(team_specialties)
                    team_copy["match_reasons"].append(f"专业领域匹配: {', '.join(matched_specs)}")
                
                if location and location in team["location"]:
                    team_copy["match_reasons"].append(f"地理位置匹配: {team['location']}")
                
                matching_teams.append(team_copy)
        
        # 按匹配分数排序
        matching_teams.sort(key=lambda x: x["match_score"], reverse=True)
        
        return matching_teams[:5]  # 返回前5个最匹配的团队

    def format_team_recommendation(self, teams: List[Dict[str, Any]]) -> str:
        """格式化律师团队推荐结果"""
        if not teams:
            return "抱歉，没有找到匹配您需求的律师团队。请尝试调整您的需求描述或提供更多信息。"

        response = f"🏛️ 律师团队推荐结果<br><br>"
        response += f"找到 **{len(teams)}** 个匹配的律师团队：<br>"

        for i, team in enumerate(teams, 1):
            # 生成星级评分显示
            stars = "⭐" * int(team['rating']) + "☆" * (5 - int(team['rating']))

            response += f"{i}. **{team['name']}** {stars} ({team['rating']}/5.0)<br>"
            response += f"所在地区: {team['location']}<br>"
            response += f"团队规模: {team['team_size']}人<br>"
            response += f"执业年限: {team['experience_years']}年<br>"
            response += f"专业领域: {', '.join(team['specialties'])}<br>"

            if team.get('match_reasons'):
                response += f"推荐理由: {'; '.join(team['match_reasons'])}<br>"

            response += f"团队简介: {team['description']}<br>"
            response += f"联系电话: {team['contact']['phone']}<br>"
            response += f"电子邮箱: {team['contact']['email']}<br>"
            response += f"办公地址: {team['contact']['address']}<br>"
            response += f"官方网站: {team['contact']['website']}<br>"

            if team.get('notable_cases'):
                response += f"典型案例: {'; '.join(team['notable_cases'])}<br>"

            if team.get('lawyers'):
                lawyers_info = []
                for lawyer in team['lawyers']:
                    lawyers_info.append(f"{lawyer['name']}({lawyer['title']}-{lawyer['specialty']})")
                response += f"核心律师: {'; '.join(lawyers_info)}<br>"

            response += "<br>"

        response += f"💡 **提示**: 建议您联系多家律师事务所进行咨询比较，选择最适合的律师团队。<br>"
        response += f"🔍 **继续咨询**: 您可以提供更多具体需求，我将为您推荐更精准的律师团队。"

        return response

    def process_recommendation_request(self, user_input: str, session_id: str = None) -> str:
        """
        处理律师推荐请求

        参数:
            user_input: 用户输入
            session_id: 会话ID

        返回:
            推荐结果
        """
        try:
            # 使用AI分析用户需求
            analysis_prompt = f"""
            用户咨询：{user_input}

            请分析用户的法律需求，提取以下信息：
            1. 主要法律问题类型
            2. 涉及的专业领域
            3. 地理位置偏好（如果有）
            4. 其他特殊要求

            请以JSON格式返回分析结果：
            {{
                "problem_type": "问题类型",
                "specialty": "专业领域",
                "location": "地理位置",
                "requirements": "具体需求描述"
            }}
            """

            # 获取AI分析结果（直接调用父类方法避免重复格式化）
            ai_response = super().chat_with_history(analysis_prompt, session_id)

            # 尝试解析JSON，如果失败则使用关键词匹配
            try:
                import re
                json_match = re.search(r'\{.*\}', ai_response, re.DOTALL)
                if json_match:
                    analysis = json.loads(json_match.group())
                    specialty = analysis.get('specialty')
                    location = analysis.get('location')
                else:
                    specialty = None
                    location = None
            except:
                specialty = None
                location = None

            # 匹配律师团队
            matching_teams = self.find_matching_teams(user_input, specialty, location)

            # 格式化推荐结果
            recommendation = self.format_team_recommendation(matching_teams)

            # 结合AI的个性化建议（直接调用父类方法避免重复格式化）
            personalized_advice = super().chat_with_history(
                f"基于用户需求：{user_input}\n\n我已为用户推荐了{len(matching_teams)}个律师团队。请提供一些个性化的选择建议和注意事项。",
                session_id
            )

            final_response = recommendation + "<br><br>" + "🤖 **AI建议：**<br>" + personalized_advice

            return final_response

        except Exception as e:
            LOG.error(f"[LawyerRecommendationAgent] 处理推荐请求失败: {str(e)}")
            return f"处理推荐请求时出现错误：{str(e)}"
