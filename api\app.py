from flask import Flask, request, jsonify
from flask_cors import CORS
import os
import sys
import logging

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from agents.scenario_agent import ScenarioAgent
from agents.conversation_agent import ConversationAgent
from agents.vocab_agent import VocabAgent
from agents.case_search_agent import CaseSearchAgent
from utils.logger import LOG

app = Flask(__name__)
CORS(app)  # 允许跨域请求

# 配置日志
logging.basicConfig(level=logging.INFO)

# 全局智能体实例
agents = {
    'scenario': {},  # 场景代理将根据场景类型动态创建
    'conversation': ConversationAgent(),
    'vocab': VocabAgent(),
    'case_search': CaseSearchAgent()
}

@app.route('/api/health', methods=['GET'])
def health_check():
    """健康检查接口"""
    return jsonify({
        'status': 'healthy',
        'message': 'LegalConsultationAssistant API is running'
    })

@app.route('/api/scenario/list', methods=['GET'])
def get_scenarios():
    """获取可用场景列表"""
    scenarios = [
        {
            'id': 'marriage_dispute',
            'name': '婚姻纠纷',
            'description': '离婚、财产分割、子女抚养等婚姻法律问题',
            'icon': 'fas fa-heart-broken'
        },
        {
            'id': 'contract_dispute',
            'name': '合同纠纷',
            'description': '合同效力、违约责任、争议解决等问题',
            'icon': 'fas fa-file-contract'
        },
        {
            'id': 'work_injury',
            'name': '工伤赔偿',
            'description': '工伤认定、伤残鉴定、赔偿计算等问题',
            'icon': 'fas fa-hard-hat'
        }
    ]
    return jsonify({
        'success': True,
        'data': scenarios
    })

@app.route('/api/scenario/<scenario_id>/intro', methods=['GET'])
def get_scenario_intro(scenario_id):
    """获取场景介绍"""
    try:
        # 获取项目根目录路径
        project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        intro_file = os.path.join(project_root, "content", "page", f"{scenario_id}.md")

        if os.path.exists(intro_file):
            with open(intro_file, 'r', encoding='utf-8') as f:
                intro_content = f.read()
            return jsonify({
                'success': True,
                'data': {
                    'intro': intro_content
                }
            })
        else:
            return jsonify({
                'success': False,
                'error': f'场景介绍文件未找到: {scenario_id}'
            }), 404
    except Exception as e:
        LOG.error(f"获取场景介绍失败: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/scenario/<scenario_id>/start', methods=['POST'])
def start_scenario(scenario_id):
    """开始场景对话"""
    try:
        data = request.get_json()
        session_id = data.get('session_id', f"{scenario_id}_default")
        
        # 创建或获取场景代理
        if scenario_id not in agents['scenario']:
            agents['scenario'][scenario_id] = ScenarioAgent(scenario_id, session_id)
        
        agent = agents['scenario'][scenario_id]
        initial_message = agent.start_new_session(session_id)
        
        return jsonify({
            'success': True,
            'data': {
                'message': initial_message,
                'session_id': session_id
            }
        })
    except Exception as e:
        LOG.error(f"开始场景对话失败: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/scenario/<scenario_id>/chat', methods=['POST'])
def scenario_chat(scenario_id):
    """场景对话"""
    try:
        data = request.get_json()
        user_input = data.get('message', '')
        session_id = data.get('session_id', f"{scenario_id}_default")
        
        if not user_input:
            return jsonify({
                'success': False,
                'error': '消息不能为空'
            }), 400
        
        # 获取或创建场景代理
        if scenario_id not in agents['scenario']:
            agents['scenario'][scenario_id] = ScenarioAgent(scenario_id, session_id)
        
        agent = agents['scenario'][scenario_id]
        bot_response = agent.chat_with_history(user_input, session_id)
        
        return jsonify({
            'success': True,
            'data': {
                'message': bot_response
            }
        })
    except Exception as e:
        LOG.error(f"场景对话失败: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/conversation/chat', methods=['POST'])
def conversation_chat():
    """自由对话"""
    try:
        data = request.get_json()
        user_input = data.get('message', '')
        session_id = data.get('session_id', 'conversation_default')
        
        if not user_input:
            return jsonify({
                'success': False,
                'error': '消息不能为空'
            }), 400
        
        agent = agents['conversation']
        bot_response = agent.chat_with_history(user_input, session_id)
        
        return jsonify({
            'success': True,
            'data': {
                'message': bot_response
            }
        })
    except Exception as e:
        LOG.error(f"自由对话失败: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/vocab/start', methods=['POST'])
def start_vocab():
    """开始法律学习"""
    try:
        data = request.get_json()
        session_id = data.get('session_id', 'vocab_default')
        book_type = data.get('book_type', None)  # 新增书籍类型参数

        agent = agents['vocab']
        agent.restart_session(session_id)

        # 如果指定了书籍类型，设置书籍记忆
        if book_type:
            agent.set_book_memory(book_type, session_id)

        # 发送初始消息
        initial_input = "开始学习"
        bot_response = agent.chat_with_history(initial_input, session_id)

        return jsonify({
            'success': True,
            'data': {
                'user_message': initial_input,
                'bot_message': bot_response,
                'session_id': session_id,
                'book_type': book_type
            }
        })
    except Exception as e:
        LOG.error(f"开始法律学习失败: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/vocab/chat', methods=['POST'])
def vocab_chat():
    """法律学习对话"""
    try:
        data = request.get_json()
        user_input = data.get('message', '')
        session_id = data.get('session_id', 'vocab_default')

        if not user_input:
            return jsonify({
                'success': False,
                'error': '消息不能为空'
            }), 400

        agent = agents['vocab']
        bot_response = agent.chat_with_history(user_input, session_id)

        # 获取当前书籍记忆信息
        book_info = agent.get_book_memory(session_id)

        return jsonify({
            'success': True,
            'data': {
                'message': bot_response,
                'book_info': book_info
            }
        })
    except Exception as e:
        LOG.error(f"法律学习对话失败: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/vocab/reset_book', methods=['POST'])
def reset_book_memory():
    """重置书籍记忆"""
    try:
        data = request.get_json()
        session_id = data.get('session_id', 'vocab_default')

        agent = agents['vocab']
        agent.restart_session(session_id)  # 这会清除书籍记忆和会话历史

        return jsonify({
            'success': True,
            'data': {
                'message': '书籍记忆已重置'
            }
        })
    except Exception as e:
        LOG.error(f"重置书籍记忆失败: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/case_search/chat', methods=['POST'])
def case_search_chat():
    """案例检索对话"""
    try:
        data = request.get_json()
        user_input = data.get('message', '')
        session_id = data.get('session_id', 'case_search_default')
        
        if not user_input:
            return jsonify({
                'success': False,
                'error': '消息不能为空'
            }), 400
        
        agent = agents['case_search']
        bot_response = agent.chat_with_history(user_input, session_id)
        
        return jsonify({
            'success': True,
            'data': {
                'message': bot_response
            }
        })
    except Exception as e:
        LOG.error(f"案例检索对话失败: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/config', methods=['POST'])
def update_config():
    """更新配置（API Key等）"""
    try:
        data = request.get_json()
        api_key = data.get('api_key')
        api_base = data.get('api_base')
        
        if api_key:
            os.environ['OPENAI_API_KEY'] = api_key
        if api_base:
            os.environ['OPENAI_API_BASE'] = api_base
        
        # 重新初始化智能体以使用新配置
        agents['conversation'] = ConversationAgent()
        agents['vocab'] = VocabAgent()
        agents['case_search'] = CaseSearchAgent()
        agents['scenario'] = {}  # 清空场景代理，将在下次使用时重新创建
        
        return jsonify({
            'success': True,
            'message': '配置更新成功'
        })
    except Exception as e:
        LOG.error(f"更新配置失败: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0', port=5000)
