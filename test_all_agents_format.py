#!/usr/bin/env python3
"""
测试所有智能体的格式化输出
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_all_agents_format():
    """测试所有智能体的格式化输出"""
    print("🧪 测试所有智能体的格式化输出...")
    
    try:
        # 测试律师推荐智能体
        print("\n1. 测试律师推荐智能体格式...")
        from agents.lawyer_recommendation_agent import LawyerRecommendationAgent
        
        lawyer_agent = LawyerRecommendationAgent.__new__(LawyerRecommendationAgent)
        lawyer_agent.lawyer_teams = lawyer_agent.load_lawyer_teams()
        
        test_teams = lawyer_agent.find_matching_teams("合同纠纷")[:1]
        lawyer_result = lawyer_agent.format_team_recommendation(test_teams)
        
        print(f"   律师推荐格式: {'✅ 包含<br>标签' if '<br>' in lawyer_result else '❌ 不包含<br>标签'}")
        print(f"   输出长度: {len(lawyer_result)} 字符")
        
        # 测试案例搜索智能体
        print("\n2. 测试案例搜索智能体格式...")
        from agents.case_search_agent import CaseSearchAgent
        
        case_agent = CaseSearchAgent.__new__(CaseSearchAgent)
        
        # 模拟案例数据
        mock_cases = [
            {
                'title': '张某盗窃案',
                'court': '北京市朝阳区人民法院',
                'crime_type': '盗窃',
                'sentence': '有期徒刑一年',
                'judgment_date': '2023-01-15',
                'case_details': '被告人张某在商场内盗窃他人财物价值2000元',
                'url': 'https://wenshu.court.gov.cn/example'
            }
        ]
        
        case_result = case_agent.format_case_response(mock_cases, "盗窃", "商场")
        
        print(f"   案例搜索格式: {'✅ 包含<br>标签' if '<br>' in case_result else '❌ 不包含<br>标签'}")
        print(f"   输出长度: {len(case_result)} 字符")
        
        # 测试基类格式化方法
        print("\n3. 测试基类格式化方法...")
        from agents.agent_base import AgentBase
        
        # 创建一个基类实例来测试格式化方法
        base_agent = AgentBase.__new__(AgentBase)
        
        # 测试不同类型的输入
        test_inputs = [
            "这是一个普通的回复",
            "这是一个\n包含换行的回复",
            "这是一个\n\n包含双换行的回复",
            "这是一个<br>已经包含br标签的回复"
        ]
        
        for i, test_input in enumerate(test_inputs, 1):
            formatted = base_agent.format_response(test_input)
            has_br = '<br>' in formatted
            print(f"   测试 {i}: {'✅ 正确格式化' if has_br or test_input == formatted else '❌ 格式化失败'}")
        
        print("\n✅ 所有智能体格式化测试完成！")
        
        # 显示格式示例
        print("\n📋 格式示例:")
        print("="*60)
        print("律师推荐格式:")
        print(lawyer_result[:200] + "..." if len(lawyer_result) > 200 else lawyer_result)
        print("\n" + "="*60)
        print("案例搜索格式:")
        print(case_result[:200] + "..." if len(case_result) > 200 else case_result)
        print("="*60)
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_all_agents_format()
    sys.exit(0 if success else 1)
