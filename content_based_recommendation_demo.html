<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>基于内容的推荐算法演示 - LCA 法律咨询助手</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f7f7f8;
            color: #202123;
        }
        .demo-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 4px 16px rgba(0,0,0,0.1);
        }
        .demo-header {
            text-align: center;
            margin-bottom: 30px;
        }
        .demo-header h1 {
            color: #10a37f;
            margin-bottom: 10px;
        }
        .algorithm-section {
            background: #dcfce7;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            border-left: 4px solid #10a37f;
        }
        .algorithm-section h3 {
            color: #10a37f;
            margin-bottom: 15px;
        }
        .algorithm-flow {
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 10px;
            margin: 20px 0;
        }
        .flow-step {
            background: white;
            padding: 15px;
            border-radius: 8px;
            text-align: center;
            flex: 1;
            min-width: 150px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            border-left: 3px solid #10a37f;
        }
        .flow-arrow {
            font-size: 1.5rem;
            color: #10a37f;
            font-weight: bold;
        }
        .comparison-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin: 20px 0;
        }
        .old-method, .new-method {
            padding: 20px;
            border-radius: 8px;
        }
        .old-method {
            background: #fee2e2;
            border-left: 4px solid #ef4444;
        }
        .new-method {
            background: #dcfce7;
            border-left: 4px solid #10a37f;
        }
        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }
        .feature-card {
            background: #f7f7f8;
            padding: 20px;
            border-radius: 8px;
            border-left: 4px solid #10a37f;
        }
        .feature-card h3 {
            color: #202123;
            margin-bottom: 10px;
        }
        .feature-list {
            list-style: none;
            padding: 0;
        }
        .feature-list li {
            padding: 5px 0;
            color: #374151;
        }
        .feature-list li::before {
            content: "✓ ";
            color: #10a37f;
            font-weight: bold;
        }
        .tech-details {
            background: #f0f9ff;
            padding: 20px;
            border-radius: 8px;
            border-left: 4px solid #0ea5e9;
            margin: 20px 0;
        }
        .tech-details h3 {
            color: #0ea5e9;
            margin-bottom: 15px;
        }
        .code-example {
            background: #1f2937;
            color: #f9fafb;
            padding: 15px;
            border-radius: 6px;
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
            overflow-x: auto;
            margin: 10px 0;
        }
        .cta-button {
            display: inline-block;
            padding: 12px 24px;
            background: #10a37f;
            color: white;
            text-decoration: none;
            border-radius: 8px;
            font-weight: 600;
            margin: 10px 10px 10px 0;
            transition: all 0.2s ease;
        }
        .cta-button:hover {
            background: #0e8f6f;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }
        @media (max-width: 768px) {
            .comparison-grid {
                grid-template-columns: 1fr;
            }
            .algorithm-flow {
                flex-direction: column;
            }
            .flow-arrow {
                transform: rotate(90deg);
            }
        }
    </style>
</head>
<body>
    <div class="demo-container">
        <div class="demo-header">
            <h1>🧠 基于内容的推荐算法上线！</h1>
            <p>律师推荐系统已升级为智能推荐算法，通过特征向量和相似度计算提供更精准的律师匹配</p>
        </div>

        <div class="algorithm-section">
            <h3>🔬 推荐算法流程</h3>
            <div class="algorithm-flow">
                <div class="flow-step">
                    <strong>1. 特征提取</strong><br>
                    <small>从用户需求中提取关键特征</small>
                </div>
                <div class="flow-arrow">→</div>
                <div class="flow-step">
                    <strong>2. 向量化</strong><br>
                    <small>转换为数值特征向量</small>
                </div>
                <div class="flow-arrow">→</div>
                <div class="flow-step">
                    <strong>3. 相似度计算</strong><br>
                    <small>计算余弦相似度</small>
                </div>
                <div class="flow-arrow">→</div>
                <div class="flow-step">
                    <strong>4. 规则调整</strong><br>
                    <small>应用匹配规则优化</small>
                </div>
                <div class="flow-arrow">→</div>
                <div class="flow-step">
                    <strong>5. 排序推荐</strong><br>
                    <small>按相似度排序输出</small>
                </div>
            </div>
        </div>

        <div class="comparison-grid">
            <div class="old-method">
                <h4>❌ 旧方法：关键词匹配</h4>
                <ul>
                    <li>简单的关键词匹配</li>
                    <li>固定的匹配规则</li>
                    <li>无法理解语义相似性</li>
                    <li>推荐结果单一</li>
                    <li>无法学习用户偏好</li>
                </ul>
            </div>
            <div class="new-method">
                <h4>✅ 新方法：基于内容推荐</h4>
                <ul>
                    <li>智能特征提取</li>
                    <li>向量化表示</li>
                    <li>语义相似度计算</li>
                    <li>多维度匹配</li>
                    <li>个性化推荐</li>
                </ul>
            </div>
        </div>

        <div class="tech-details">
            <h3>⚙️ 技术实现细节</h3>
            <div style="margin-bottom: 15px;">
                <strong>1. 特征提取技术</strong>
                <div class="code-example">
# 使用jieba分词和TF-IDF提取特征
keywords = jieba.analyse.extract_tags(text, topK=20, withWeight=True)
features = {word: weight * domain_weight for word, weight in keywords}
                </div>
            </div>
            
            <div style="margin-bottom: 15px;">
                <strong>2. 相似度计算公式</strong>
                <div class="code-example">
# 余弦相似度计算
similarity = dot_product(vector1, vector2) / (magnitude1 * magnitude2)
                </div>
            </div>
            
            <div style="margin-bottom: 15px;">
                <strong>3. 权重调整策略</strong>
                <div class="code-example">
# 多因素权重调整
adjusted_similarity = similarity * rating_weight * experience_weight * location_weight
                </div>
            </div>
        </div>

        <div class="feature-grid">
            <div class="feature-card">
                <h3>🎯 智能特征提取</h3>
                <ul class="feature-list">
                    <li>法律领域关键词识别</li>
                    <li>地域信息自动提取</li>
                    <li>专业术语权重调整</li>
                    <li>停用词过滤优化</li>
                </ul>
            </div>

            <div class="feature-card">
                <h3>📊 向量化处理</h3>
                <ul class="feature-list">
                    <li>TF-IDF权重计算</li>
                    <li>领域词汇加权</li>
                    <li>多维特征向量</li>
                    <li>稀疏向量优化</li>
                </ul>
            </div>

            <div class="feature-card">
                <h3>🔍 相似度计算</h3>
                <ul class="feature-list">
                    <li>余弦相似度算法</li>
                    <li>语义相似性分析</li>
                    <li>多维度匹配评分</li>
                    <li>实时计算优化</li>
                </ul>
            </div>

            <div class="feature-card">
                <h3>⚖️ 规则优化</h3>
                <ul class="feature-list">
                    <li>律师评分权重</li>
                    <li>经验年限加权</li>
                    <li>地域匹配优先</li>
                    <li>专业领域精确匹配</li>
                </ul>
            </div>
        </div>

        <div style="background: #fff3cd; padding: 20px; border-radius: 8px; border-left: 4px solid #f59e0b; margin: 20px 0;">
            <h3>📈 算法优势</h3>
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin-top: 15px;">
                <div>
                    <strong>精准度提升</strong><br>
                    <small>通过语义分析提高匹配精度</small>
                </div>
                <div>
                    <strong>个性化推荐</strong><br>
                    <small>基于用户具体需求定制推荐</small>
                </div>
                <div>
                    <strong>多维度评估</strong><br>
                    <small>综合考虑多个匹配因素</small>
                </div>
                <div>
                    <strong>可扩展性强</strong><br>
                    <small>易于添加新的特征和规则</small>
                </div>
            </div>
        </div>

        <div style="background: #f0f9ff; padding: 20px; border-radius: 8px; border-left: 4px solid #0ea5e9; margin: 20px 0;">
            <h3>🧪 测试示例</h3>
            <div style="background: white; padding: 15px; border-radius: 6px; margin: 10px 0;">
                <strong>用户输入：</strong>"我需要一个专业的合同纠纷律师，最好在北京"<br><br>
                <strong>算法处理：</strong><br>
                1. 提取特征：["合同", "纠纷", "律师", "专业", "北京"]<br>
                2. 计算权重：合同(2.0), 纠纷(1.5), 北京(1.2)<br>
                3. 匹配律师团队特征向量<br>
                4. 计算相似度并排序<br>
                5. 应用地域和专业加权<br><br>
                <strong>推荐结果：</strong>按相似度排序的北京合同法专业律师团队
            </div>
        </div>

        <div style="text-align: center; margin-top: 30px;">
            <h3>🚀 体验智能推荐算法</h3>
            <p>新的基于内容的推荐算法为您提供更精准、更个性化的律师推荐服务！</p>
            
            <a href="http://localhost:8000" class="cta-button" target="_blank">
                体验智能推荐
            </a>
            
            <a href="javascript:void(0)" class="cta-button" onclick="testRecommendation()">
                测试推荐算法
            </a>
        </div>

        <div style="margin-top: 30px; padding: 20px; background: #dcfce7; border-radius: 8px; border-left: 4px solid #10a37f;">
            <h3>📋 测试建议</h3>
            <ol>
                <li>尝试不同类型的法律需求描述</li>
                <li>观察推荐结果的相关性</li>
                <li>测试地域和专业领域的匹配效果</li>
                <li>比较详细描述和简单描述的推荐差异</li>
                <li>验证推荐结果的排序合理性</li>
            </ol>
        </div>

        <div style="margin-top: 20px; padding: 20px; background: #f0f9ff; border-radius: 8px; border-left: 4px solid #0ea5e9;">
            <h3>🔮 未来优化方向</h3>
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 15px; margin-top: 15px;">
                <div>
                    <strong>协同过滤</strong><br>
                    <small>基于用户行为的推荐</small>
                </div>
                <div>
                    <strong>深度学习</strong><br>
                    <small>神经网络推荐模型</small>
                </div>
                <div>
                    <strong>实时学习</strong><br>
                    <small>根据反馈动态调整</small>
                </div>
                <div>
                    <strong>混合推荐</strong><br>
                    <small>多种算法融合</small>
                </div>
            </div>
        </div>
    </div>

    <script>
        function testRecommendation() {
            window.open('http://localhost:8000', '_blank');
            setTimeout(() => {
                alert('推荐算法测试指南：\n\n1. 点击左侧"律师推荐"\n2. 尝试输入不同的需求：\n   • "我需要合同纠纷律师"\n   • "寻找北京知识产权专业律师"\n   • "需要经验丰富的刑事辩护律师"\n3. 观察推荐结果：\n   • 专业领域匹配度\n   • 地域相关性\n   • 排序合理性\n\n请在新窗口中进行测试！');
            }, 1000);
        }
    </script>
</body>
</html>
