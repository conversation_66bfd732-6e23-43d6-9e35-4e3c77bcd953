#!/usr/bin/env python3
"""
测试从JSON文件加载律师团队数据
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_lawyer_json():
    """测试从JSON文件加载律师团队数据"""
    print("🧪 测试从JSON文件加载律师团队数据...")
    
    try:
        from agents.lawyer_recommendation_agent import LawyerRecommendationAgent
        
        # 创建律师推荐智能体
        agent = LawyerRecommendationAgent()
        print(f"✅ 律师推荐智能体创建成功")
        
        # 检查加载的律师团队数据
        print(f"📊 已加载 {len(agent.lawyer_teams)} 个律师团队")
        
        # 显示所有律师团队
        print("\n📋 律师团队列表:")
        for i, team in enumerate(agent.lawyer_teams, 1):
            print(f"{i}. {team['name']} - {team['location']} - {', '.join(team['specialties'][:3])}...")
        
        # 测试匹配功能
        test_cases = [
            "我需要处理一个电子商务纠纷",
            "我在杭州，需要数据保护方面的律师",
            "我需要环境法律师",
            "我在武汉遇到了医疗事故",
            "我需要处理海商法相关的国际贸易纠纷"
        ]
        
        print("\n🔍 测试匹配功能:")
        for i, test_case in enumerate(test_cases, 1):
            print(f"\n测试案例 {i}: {test_case}")
            
            # 测试匹配算法
            matching_teams = agent.find_matching_teams(test_case)
            print(f"   匹配到 {len(matching_teams)} 个律师团队")
            
            if matching_teams:
                for j, team in enumerate(matching_teams[:3], 1):  # 显示前3个
                    score = team.get('match_score', 0)
                    reasons = team.get('match_reasons', [])
                    print(f"   {j}. {team['name']} (分数: {score:.1f}) - {'; '.join(reasons) if reasons else '基础匹配'}")
        
        print("\n✅ 律师团队JSON数据测试完成！")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_lawyer_json()
    sys.exit(0 if success else 1)
